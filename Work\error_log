[20-Jul-2025 16:59:28 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php:113
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php(63): Theme25\Backend\Controller\Customer\Fakedetector\Scan->prepareManualScanData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php(52): Theme25\Backend\Controller\Customer\Fakedetector\Scan->showManualScanForm()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Scan->manual()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(218): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('Theme25/Backend...', 'manual', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/modification in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php on line 113
[20-Jul-2025 17:51:42 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:124
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(40): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 124
[20-Jul-2025 17:54:17 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:129
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(40): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 129
[20-Jul-2025 17:58:44 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:125
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(38): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 125
[20-Jul-2025 18:00:43 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:125
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(38): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 125
[20-Jul-2025 18:06:46 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:125
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(38): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 125
[20-Jul-2025 18:07:59 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:125
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(38): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 125
[20-Jul-2025 18:09:46 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:127
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(38): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 127
[20-Jul-2025 18:13:29 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:128
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(38): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 128
[20-Jul-2025 15:28:47 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:127
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(35): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 127
[20-Jul-2025 15:30:11 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:127
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(35): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 127
[20-Jul-2025 15:35:41 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getBlacklist() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Blacklist.php:101
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Blacklist.php(45): Theme25\Backend\Controller\Customer\Fakedetector\Blacklist->prepareBlacklistData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Blacklist.php(34): Theme25\Backend\Controller\Customer\Fakedetector\Blacklist->showBlacklistPage()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Blacklist.php(23): Theme25\Backend\Controller\Customer\Fakedetector\Blacklist->execute()
#3 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Blacklist->index()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...',  in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Blacklist.php on line 101
[20-Jul-2025 15:39:27 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:127
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(35): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 127
[20-Jul-2025 18:40:25 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:127
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(35): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 127
[20-Jul-2025 15:41:52 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:127
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(35): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 127
[20-Jul-2025 15:41:57 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:127
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(35): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 127
[20-Jul-2025 18:42:25 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:127
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(35): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 127
[20-Jul-2025 18:47:35 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function query() on null in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php:61
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(29): Theme25\Backend\Helper\BaseHelper->query('SELECT * FROM o...')
#1 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(16): Theme25\Backend\Helper\Fakedetectorhelper->loadSettings()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(17): Theme25\Backend\Helper\Fakedetectorhelper->__construct(NULL, NULL)
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(214): Theme25\Backend\Controller\Customer\Fakedetector\Settings->__construct(Object(Registry))
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
 in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php on line 61
[20-Jul-2025 16:00:36 UTC] PHP Fatal error:  Uncaught Error: Call to a member function query() on null in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php:61
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(29): Theme25\Backend\Helper\BaseHelper->query('SELECT * FROM o...')
#1 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(16): Theme25\Backend\Helper\Fakedetectorhelper->loadSettings()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(17): Theme25\Backend\Helper\Fakedetectorhelper->__construct(NULL, NULL)
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(214): Theme25\Backend\Controller\Customer\Fakedetector\Settings->__construct(Object(Registry))
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
 in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php on line 61
[20-Jul-2025 19:01:17 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function query() on null in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php:61
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(29): Theme25\Backend\Helper\BaseHelper->query('SELECT * FROM o...')
#1 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(16): Theme25\Backend\Helper\Fakedetectorhelper->loadSettings()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(19): Theme25\Backend\Helper\Fakedetectorhelper->__construct(NULL, NULL)
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(214): Theme25\Backend\Controller\Customer\Fakedetector\Settings->__construct(Object(Registry))
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
 in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php on line 61
[20-Jul-2025 19:02:25 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function query() on null in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php:61
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(29): Theme25\Backend\Helper\BaseHelper->query('SELECT * FROM o...')
#1 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(16): Theme25\Backend\Helper\Fakedetectorhelper->loadSettings()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(20): Theme25\Backend\Helper\Fakedetectorhelper->__construct(NULL, NULL)
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(214): Theme25\Backend\Controller\Customer\Fakedetector\Settings->__construct(Object(Registry))
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
 in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php on line 61
[20-Jul-2025 19:07:25 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function query() on null in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php:61
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(29): Theme25\Backend\Helper\BaseHelper->query('SELECT * FROM o...')
#1 /home/<USER>/storage_theme25/theme/Backend/Helper/Fakedetectorhelper.php(16): Theme25\Backend\Helper\Fakedetectorhelper->loadSettings()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(20): Theme25\Backend\Helper\Fakedetectorhelper->__construct(NULL, NULL)
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(214): Theme25\Backend\Controller\Customer\Fakedetector\Settings->__construct(Object(Registry))
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
 in /home/<USER>/storage_theme25/theme/Backend/Helper/BaseHelper.php on line 61
[20-Jul-2025 19:23:50 Europe/Sofia] PHP Fatal error:  Uncaught Exception: Invalid controller object passed to sub-controller! in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php:21
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(14): Theme25\ControllerSubMethods->__construct(Object(Registry))
#1 /home/<USER>/storage_theme25/theme/RequestProcessor.php(214): Theme25\Backend\Controller\Customer\Fakedetector\Settings->__construct(Object(Registry))
#2 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#3 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#4 /home/<USER>/storage_theme25/modification/system/engine/action.php(90): RequestProcessor->process('ControllerCusto...', 'index', Array)
#5 /home/<USER>/storage_theme25/modification/system/engine/action.php(74): Action->callRequestProcessor('ControllerCusto...', 'index', Array,  in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php on line 21
[20-Jul-2025 19:25:55 Europe/Sofia] PHP Fatal error:  Uncaught Exception: Invalid controller object passed to sub-controller! in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php:22
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(14): Theme25\ControllerSubMethods->__construct(Object(Registry))
#1 /home/<USER>/storage_theme25/theme/RequestProcessor.php(214): Theme25\Backend\Controller\Customer\Fakedetector\Settings->__construct(Object(Registry))
#2 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#3 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#4 /home/<USER>/storage_theme25/modification/system/engine/action.php(90): RequestProcessor->process('ControllerCusto...', 'index', Array)
#5 /home/<USER>/storage_theme25/modification/system/engine/action.php(74): Action->callRequestProcessor('ControllerCusto...', 'index', Array,  in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php on line 22
[20-Jul-2025 19:36:54 Europe/Sofia] PHP Fatal error:  Uncaught Exception: Invalid controller object passed to sub-controller! in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php:17
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(14): Theme25\ControllerSubMethods->__construct(Object(Theme25\Backend\Controller\Customer\Fakedetector))
#1 /home/<USER>/storage_theme25/theme/RequestProcessor.php(214): Theme25\Backend\Controller\Customer\Fakedetector\Settings->__construct(Object(Registry))
#2 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#3 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#4 /home/<USER>/storage_theme25/modification/system/engine/action.php(90): RequestProcessor->process('ControllerCusto...', 'index', Array)
#5 /home/<USER>/storage_theme25/modification/system/engine/action.php(74): Action->callRequestProcesso in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php on line 17
[20-Jul-2025 19:37:20 Europe/Sofia] PHP Fatal error:  Uncaught Exception: Invalid controller object passed to sub-controller! in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php:17
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Index.php(10): Theme25\ControllerSubMethods->__construct(Object(Theme25\Backend\Controller\Customer\Fakedetector))
#1 /home/<USER>/storage_theme25/theme/Controller.php(664): Theme25\Backend\Controller\Customer\Fakedetector\Index->__construct(Object(Theme25\Backend\Controller\Customer\Fakedetector))
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector.php(15): Theme25\Controller->setBackendSubController('Customer/Fakede...', Object(Theme25\Backend\Controller\Customer\Fakedetector))
#3 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector->index()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAn in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php on line 17
[20-Jul-2025 19:40:09 Europe/Sofia] PHP Fatal error:  Uncaught Exception: Invalid controller object passed to sub-controller! in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php:28
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Index.php(10): Theme25\ControllerSubMethods->__construct(Object(Theme25\Backend\Controller\Customer\Fakedetector))
#1 /home/<USER>/storage_theme25/theme/Controller.php(664): Theme25\Backend\Controller\Customer\Fakedetector\Index->__construct(Object(Theme25\Backend\Controller\Customer\Fakedetector))
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector.php(15): Theme25\Controller->setBackendSubController('Customer/Fakede...', Object(Theme25\Backend\Controller\Customer\Fakedetector))
#3 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector->index()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAn in /home/<USER>/storage_theme25/theme/ControllerSubMethods.php on line 28
[20-Jul-2025 19:49:56 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to undefined method Theme25\Backend\Helper\Fakedetectorhelper::runScan() in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php:35
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php(21): Theme25\Backend\Controller\Customer\Fakedetector\Scan->execute()
#1 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Scan->index()
#2 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#4 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#5 /home/<USER>/storage_theme25/modification/system/engine/action.php(90): RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/modification/system/engine/ac in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php on line 35
[21-Jul-2025 08:16:44 UTC] PHP Fatal error:  Uncaught Error: Call to a member function query() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Install.php:15
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Install.php(152): Theme25\Backend\Controller\Customer\Fakedetector\Install->dbQuery('SHOW TABLES LIK...')
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Install.php(45): Theme25\Backend\Controller\Customer\Fakedetector\Install->checkTables()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector.php(20): Theme25\Backend\Controller\Customer\Fakedetector\Install->createTablesIfNeeded()
#3 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector.php(11): Theme25\Backend\Controller\Customer\Fakedetector->ensureTablesExist()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(214): Theme25\Backend\Controller\Customer\Fakedetector->__construct(Object(Registry))
#5 /home/<USER>/storage_theme25/theme/Req in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Install.php on line 15
