<?php

namespace Theme25\Backend\Controller\Customer\Fakedetector;

class Blacklist extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('fake_detector_blacklist.log');
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'fake-detector-blacklist.js',
        ]);
    }

    public function index() {
        return $this->execute();
    }

    /**
     * Основна страница за управление на черния списък
     * (адаптирано от оригиналния fake_detector_blacklist.php)
     */
    public function execute() {
        if ($this->isPostRequest()) {
            return $this->processBlacklistAction();
        } else {
            return $this->showBlacklistPage();
        }
    }

    /**
     * Показва страницата с черния списък
     */
    private function showBlacklistPage() {
        $this->setTitle('Черен списък');
        $this->initAdminData();
        
        $this->prepareBlacklistData()
             ->prepareFilterOptions()
             ->preparePagination()
             ->prepareActionButtons();
        
        $this->renderTemplateWithDataAndOutput('customer/fake_detector_blacklist');
    }

    /**
     * Обработва действията с черния списък (добавяне/премахване)
     */
    private function processBlacklistAction() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $action = $this->requestPost('action', '');
            
            switch ($action) {
                case 'add':
                    $json = $this->addToBlacklist();
                    break;
                case 'remove':
                    $json = $this->removeFromBlacklist();
                    break;
                case 'bulk_remove':
                    $json = $this->bulkRemoveFromBlacklist();
                    break;
                default:
                    $json['error'] = 'Неизвестно действие!';
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Подготвя данните за черния списък
     */
    private function prepareBlacklistData() {
        $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
        
        // Филтри от заявката
        $filter_data = [
            'filter_type' => $this->requestGet('filter_type', ''),
            'filter_value' => $this->requestGet('filter_value', ''),
            'filter_date_from' => $this->requestGet('filter_date_from', ''),
            'filter_date_to' => $this->requestGet('filter_date_to', ''),
            'sort' => $this->requestGet('sort', 'date_added'),
            'order' => $this->requestGet('order', 'DESC'),
            'start' => ($this->requestGet('page', 1) - 1) * $this->getConfig('config_limit_admin'),
            'limit' => $this->getConfig('config_limit_admin')
        ];

        // Получаване на записите от черния списък
        $blacklist_items = $this->fakeDetectorModel->getBlacklist($filter_data);
        $total_items = $this->fakeDetectorModel->getTotalBlacklistItems($filter_data);

        // Подготовка на данните за изгледа
        $blacklist = [];
        foreach ($blacklist_items as $item) {
            $blacklist[] = [
                'id' => $item['id'],
                'type' => $item['type'],
                'type_text' => $this->getTypeText($item['type']),
                'value' => $item['value'],
                'note' => $item['note'],
                'date_added' => date('d.m.Y H:i', strtotime($item['date_added'])),
                'remove_url' => $this->getAdminLink('customer/fakedetector/blacklist', 'action=remove&id=' . $item['id']),
                'affected_customers' => $this->fakeDetectorModel->getAffectedCustomersCount($item['type'], $item['value'])
            ];
        }

        // Sorting URLs
        $sort = $filter_data['sort'];
        $order = $filter_data['order'];
        $url_params = '';
        foreach (['filter_type', 'filter_value', 'filter_date_from', 'filter_date_to'] as $key) {
            if (!empty($filter_data[$key])) {
                $url_params .= '&' . $key . '=' . urlencode($filter_data[$key]);
            }
        }

        // Type options for filter
        $type_options = [
            '' => 'Всички типове',
            'ip' => 'IP адреси',
            'email' => 'Email домейни'
        ];

        $this->setData([
            'blacklist' => $blacklist,
            'total_items' => $total_items,
            'filter_type' => $filter_data['filter_type'],
            'filter_value' => $filter_data['filter_value'],
            'filter_date_from' => $filter_data['filter_date_from'],
            'filter_date_to' => $filter_data['filter_date_to'],
            'sort' => $filter_data['sort'],
            'order' => $filter_data['order'],
            'type_options' => $type_options,
            'back_url' => $this->getAdminLink('customer/fakedetector'),
            'add_url' => $this->getAdminLink('customer/fakedetector/blacklist/add'),
            'remove_url' => $this->getAdminLink('customer/fakedetector/blacklist/remove'),
            'export_url' => $this->getAdminLink('customer/fakedetector/blacklist/export'),
            'import_url' => $this->getAdminLink('customer/fakedetector/blacklist/import'),
            // Sorting URLs
            'sort_type' => $this->getAdminLink('customer/fakedetector/blacklist', 'sort=type&order=' . (($sort == 'type' && $order == 'ASC') ? 'DESC' : 'ASC') . $url_params),
            'sort_value' => $this->getAdminLink('customer/fakedetector/blacklist', 'sort=value&order=' . (($sort == 'value' && $order == 'ASC') ? 'DESC' : 'ASC') . $url_params),
            'sort_date_added' => $this->getAdminLink('customer/fakedetector/blacklist', 'sort=date_added&order=' . (($sort == 'date_added' && $order == 'ASC') ? 'DESC' : 'ASC') . $url_params)
        ]);

        return $this;
    }

    /**
     * Подготвя опциите за филтриране
     */
    private function prepareFilterOptions() {
        // Типове записи
        $types = [
            '' => 'Всички типове',
            'ip' => 'IP адреси',
            'email' => 'Email домейни'
        ];

        $this->setData([
            'type_options' => $types
        ]);

        return $this;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $page = $this->requestGet('page', 1);
        $limit = $this->getConfig('config_limit_admin');
        $total = $this->getData('total_items');

        $pagination = new \Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $limit;
        $pagination->url = $this->getAdminLink('customer/fakedetector/blacklist', 'page={page}');

        $this->setData([
            'pagination' => $pagination->render(),
            'results' => sprintf(
                'Показани %d до %d от %d (%d страници)',
                ($page - 1) * $limit + 1,
                min($page * $limit, $total),
                $total,
                ceil($total / $limit)
            )
        ]);

        return $this;
    }

    /**
     * Подготвя бутоните за действия
     */
    private function prepareActionButtons() {
        $this->setData([
            'add_action' => $this->getAdminLink('customer/fakedetector/blacklist'),
            'back_url' => $this->getAdminLink('customer/fakedetector'),
            'export_url' => $this->getAdminLink('customer/fakedetector/export', 'type=blacklist'),
            'import_url' => $this->getAdminLink('customer/fakedetector/import', 'type=blacklist')
        ]);

        return $this;
    }

    /**
     * Добавя запис в черния списък
     */
    private function addToBlacklist() {
        $type = $this->requestPost('type', '');
        $value = trim($this->requestPost('value', ''));
        $note = trim($this->requestPost('note', ''));

        // Валидация
        $errors = $this->validateBlacklistEntry($type, $value);
        if (!empty($errors)) {
            return ['error' => implode(' ', $errors)];
        }

        try {
            $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
            
            // Проверка дали записът вече съществува
            if ($this->fakeDetectorModel->blacklistEntryExists($type, $value)) {
                return ['error' => 'Този запис вече съществува в черния списък!'];
            }

            // Добавяне на записа
            $result = $this->fakeDetectorModel->addToBlacklist($type, $value, $note);
            
            if ($result) {
                return [
                    'success' => 'Записът е добавен успешно в черния списък!',
                    'reload' => true
                ];
            } else {
                return ['error' => 'Грешка при добавяне на записа!'];
            }
            
        } catch (Exception $e) {
            return ['error' => 'Грешка при добавяне: ' . $e->getMessage()];
        }
    }

    /**
     * Премахва запис от черния списък
     */
    private function removeFromBlacklist() {
        $id = (int)$this->requestPost('id', 0);
        
        if (!$id) {
            return ['error' => 'Невалиден ID на запис!'];
        }

        try {
            $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
            
            $result = $this->fakeDetectorModel->removeFromBlacklist($id);
            
            if ($result) {
                return [
                    'success' => 'Записът е премахнат успешно от черния списък!',
                    'reload' => true
                ];
            } else {
                return ['error' => 'Грешка при премахване на записа!'];
            }
            
        } catch (Exception $e) {
            return ['error' => 'Грешка при премахване: ' . $e->getMessage()];
        }
    }

    /**
     * Масово премахване на записи от черния списък
     */
    private function bulkRemoveFromBlacklist() {
        $ids = $this->requestPost('ids', []);
        
        if (empty($ids) || !is_array($ids)) {
            return ['error' => 'Не са избрани записи за премахване!'];
        }

        try {
            $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
            
            $removed_count = 0;
            foreach ($ids as $id) {
                if ($this->fakeDetectorModel->removeFromBlacklist((int)$id)) {
                    $removed_count++;
                }
            }
            
            return [
                'success' => sprintf('Премахнати са %d записа от черния списък!', $removed_count),
                'reload' => true
            ];
            
        } catch (Exception $e) {
            return ['error' => 'Грешка при масово премахване: ' . $e->getMessage()];
        }
    }

    /**
     * Валидира запис за черния списък
     */
    private function validateBlacklistEntry($type, $value) {
        $errors = [];

        if (empty($type) || !in_array($type, ['ip', 'email'])) {
            $errors[] = 'Невалиден тип на запис!';
        }

        if (empty($value)) {
            $errors[] = 'Стойността не може да бъде празна!';
        }

        if ($type === 'ip' && !filter_var($value, FILTER_VALIDATE_IP)) {
            $errors[] = 'Невалиден IP адрес!';
        }

        if ($type === 'email' && !filter_var('test@' . $value, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Невалиден email домейн!';
        }

        return $errors;
    }

    /**
     * Връща текстовото представяне на типа
     */
    private function getTypeText($type) {
        $types = [
            'ip' => 'IP адрес',
            'email' => 'Email домейн'
        ];

        return isset($types[$type]) ? $types[$type] : $type;
    }

    /**
     * AJAX метод за автодопълване на стойности
     */
    public function autocomplete() {
        $json = [];
        
        $type = $this->requestGet('type', '');
        $query = $this->requestGet('q', '');
        
        if ($type && $query) {
            $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
            $suggestions = $this->fakeDetectorModel->getBlacklistSuggestions($type, $query);
            $json = $suggestions;
        }

        $this->setJSONResponseOutput($json);
    }
}
