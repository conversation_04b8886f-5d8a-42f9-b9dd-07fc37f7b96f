<!-- Settings Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
  <div class="flex flex-col md:flex-row md:items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Настройки на Fake Detector</h1>
      <p class="text-gray-500 mt-1">Конфигуриране на правилата за откриване на фалшиви регистрации</p>
    </div>
    <div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
      <a href="{{ back_url }}" class="px-4 py-2 bg-gray-600 text-white rounded-button hover:bg-gray-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-arrow-left-line"></i>
        </div>
        <span>Назад</span>
      </a>
      <button type="button" id="button-test-settings" class="px-4 py-2 bg-blue-600 text-white rounded-button hover:bg-blue-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-flask-line"></i>
        </div>
        <span>Тест</span>
      </button>
      <button type="button" id="button-reset-settings" class="px-4 py-2 bg-orange-600 text-white rounded-button hover:bg-orange-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-restart-line"></i>
        </div>
        <span>Възстанови</span>
      </button>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="p-6">
  <!-- Alert Messages -->
  {% if error_warning %}
  <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-error-warning-line text-red-500 mr-2"></i>
      <span class="text-red-700">{{ error_warning }}</span>
    </div>
  </div>
  {% endif %}

  {% if success %}
  <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-check-line text-green-500 mr-2"></i>
      <span class="text-green-700">{{ success }}</span>
    </div>
  </div>
  {% endif %}

  <form id="form-settings" method="post" action="{{ action_url }}">
    
    <!-- Detection Rules Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
          <i class="ri-search-line mr-2"></i>
          Правила за детекция
        </h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          <!-- Username Rules -->
          <div class="space-y-4">
            <h4 class="text-md font-medium text-gray-800">Правила за потребителски имена</h4>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2" for="min-username-length">Минимална дължина на име</label>
              <input type="number" name="settings[min_username_length]" id="min-username-length" 
                     value="{{ all_settings.min_username_length|default(10) }}" min="1" max="50"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
              <p class="text-xs text-gray-500 mt-1">Имена по-къси от тази дължина няма да се считат за подозрителни</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2" for="regex-username">Regex за подозрителни имена</label>
              <input type="text" name="settings[regex_username]" id="regex-username" 
                     value="{{ all_settings.regex_username|default('/^[a-zA-Z0-9]{10,}$/') }}"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-mono text-sm" />
              <p class="text-xs text-gray-500 mt-1">Регулярен израз за откриване на подозрителни имена</p>
            </div>
          </div>
          
          <!-- IP Rules -->
          <div class="space-y-4">
            <h4 class="text-md font-medium text-gray-800">Правила за IP адреси</h4>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2" for="max-accounts-per-ip">Максимум акаунти на IP</label>
              <input type="number" name="settings[max_accounts_per_ip]" id="max-accounts-per-ip" 
                     value="{{ all_settings.max_accounts_per_ip|default(3) }}" min="1" max="100"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
              <p class="text-xs text-gray-500 mt-1">IP адреси с повече от този брой регистрации ще се считат за подозрителни</p>
            </div>
            
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" name="settings[check_blacklist]" value="1" 
                       {% if all_settings.check_blacklist %}checked{% endif %}
                       class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
                <span class="text-sm font-medium text-gray-700">Проверка в черен списък</span>
              </label>
              <p class="text-xs text-gray-500 ml-6">Проверява IP адреси и email домейни в черния списък</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Behavior Rules Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
          <i class="ri-user-settings-line mr-2"></i>
          Правила за поведение
        </h3>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <label class="flex items-center">
            <input type="checkbox" name="settings[only_no_orders]" value="1" 
                   {% if all_settings.only_no_orders %}checked{% endif %}
                   class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
            <span class="text-sm font-medium text-gray-700">Само потребители без поръчки</span>
          </label>
          <p class="text-xs text-gray-500 ml-6">Маркира само потребители, които не са направили нито една поръчка</p>
        </div>
      </div>
    </div>

    <!-- Bad Domains Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
          <i class="ri-mail-forbid-line mr-2"></i>
          Съмнителни домейни
        </h3>
      </div>
      <div class="p-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2" for="bad-domains">Списък със съмнителни домейни</label>
          <textarea name="settings[bad_domains_text]" id="bad-domains" rows="10"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-mono text-sm"
                    placeholder="hotmail.com&#10;mail.ru&#10;tempmail.org&#10;...">{% if all_settings.bad_domains %}{{ all_settings.bad_domains|join('\n') }}{% endif %}</textarea>
          <p class="text-xs text-gray-500 mt-1">Въведете по един домейн на ред. Email адреси от тези домейни ще се считат за подозрителни.</p>
        </div>
      </div>
    </div>

    <!-- Automation Settings Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
          <i class="ri-timer-line mr-2"></i>
          Автоматизация
        </h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <label class="flex items-center">
              <input type="checkbox" name="settings[auto_scan_enabled]" value="1" 
                     {% if all_settings.auto_scan_enabled %}checked{% endif %}
                     class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
              <span class="text-sm font-medium text-gray-700">Автоматично сканиране</span>
            </label>
            <p class="text-xs text-gray-500 ml-6">Включва автоматично сканиране на нови регистрации</p>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2" for="scan-interval">Интервал на сканиране (часове)</label>
              <input type="number" name="settings[scan_interval_hours]" id="scan-interval" 
                     value="{{ all_settings.scan_interval_hours|default(24) }}" min="1" max="168"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
            </div>
          </div>
          
          <div class="space-y-4">
            <label class="flex items-center">
              <input type="checkbox" name="settings[test_mode]" value="1" 
                     {% if all_settings.test_mode %}checked{% endif %}
                     class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
              <span class="text-sm font-medium text-gray-700">Тестов режим по подразбиране</span>
            </label>
            <p class="text-xs text-gray-500 ml-6">Новите сканирания ще стартират в тестов режим</p>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2" for="delete-after">Изтриване след (дни)</label>
              <input type="number" name="settings[delete_after_days]" id="delete-after" 
                     value="{{ all_settings.delete_after_days|default(30) }}" min="0" max="365"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
              <p class="text-xs text-gray-500 mt-1">0 = никога не изтривай автоматично</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- System Settings Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
          <i class="ri-settings-2-line mr-2"></i>
          Системни настройки
        </h3>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <label class="flex items-center">
            <input type="checkbox" name="settings[enable_logging]" value="1" 
                   {% if all_settings.enable_logging %}checked{% endif %}
                   class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
            <span class="text-sm font-medium text-gray-700">Детайлно логване</span>
          </label>
          <p class="text-xs text-gray-500 ml-6">Записва детайлни логове за всички операции на модула</p>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4">
        <div class="flex flex-col sm:flex-row gap-3 justify-between">
          <div class="flex gap-3">
            <button type="submit" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors flex items-center">
              <i class="ri-save-line mr-2"></i>
              Запази настройките
            </button>
            <button type="button" id="button-export" class="px-4 py-2 bg-blue-600 text-white rounded-button hover:bg-blue-700 transition-colors flex items-center">
              <i class="ri-download-line mr-2"></i>
              Експорт
            </button>
          </div>
          <div>
            <label for="import-file" class="px-4 py-2 bg-green-600 text-white rounded-button hover:bg-green-700 transition-colors flex items-center cursor-pointer">
              <i class="ri-upload-line mr-2"></i>
              Импорт
            </label>
            <input type="file" id="import-file" accept=".json" class="hidden" />
          </div>
        </div>
      </div>
    </div>

  </form>
</div>

<!-- Test Results Modal -->
<div id="test-results-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Резултати от теста</h3>
    </div>
    <div class="p-6" id="test-results-content">
      <!-- Съдържанието ще се зареди динамично -->
    </div>
    <div class="px-6 py-4 border-t border-gray-200">
      <button type="button" id="close-test-modal" class="px-4 py-2 bg-gray-500 text-white rounded-button hover:bg-gray-600 transition-colors">
        Затвори
      </button>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div id="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg p-6 flex items-center">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-3"></div>
    <span class="text-gray-700">Зареждане...</span>
  </div>
</div>

<script>
// Configuration for the settings page
window.settingsConfig = {
  userToken: '{{ user_token }}',
  actionUrl: '{{ action_url }}',
  testUrl: '{{ test_scan_url }}',
  resetUrl: '{{ reset_url }}',
  exportUrl: '{{ export_url }}',
  importUrl: '{{ import_url }}'
};
</script>
