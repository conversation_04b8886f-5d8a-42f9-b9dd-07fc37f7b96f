/**
 * Fake Detector Blacklist Module JavaScript
 * Следва BackendModule pattern на Rakla.bg проекта
 */

(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initFakeDetectorBlacklist();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        Object.assign(BackendModule, {

            // Конфигурация за Blacklist
            fakeDetectorBlacklist: {
                config: null,
                elements: {},
                isInitialized: false,
                validation: {
                    ip: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
                    domain: /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/
                }
            },

            // Инициализация на Blacklist модула
            initFakeDetectorBlacklist: function() {
                if (this.fakeDetectorBlacklist.isInitialized) return;

                // Проверка дали сме на Blacklist страница
                if (!window.blacklistConfig) return;

                this.fakeDetectorBlacklist.config = window.blacklistConfig;
                this.cacheBlacklistElements();
                this.bindBlacklistEvents();
                this.initBlacklistComponents();

                this.fakeDetectorBlacklist.isInitialized = true;
                this.log('Fake Detector Blacklist module initialized');
            },

            // Кеширане на DOM елементи
            cacheBlacklistElements: function() {
                this.fakeDetectorBlacklist.elements = {
                    // Основни форми и таблици
                    blacklistForm: document.getElementById('form-blacklist'),
                    blacklistTable: document.querySelector('.table-responsive table'),
                    addForm: document.getElementById('add-form'),
                    addFormElement: document.getElementById('form-add-blacklist'),

                    // Филтри
                    filterType: document.getElementById('filter-type'),
                    filterValue: document.getElementById('filter-value'),
                    filterDateFrom: document.getElementById('filter-date-from'),
                    filterDateTo: document.getElementById('filter-date-to'),

                    // Add form inputs
                    addType: document.getElementById('input-add-type'),
                    addValue: document.getElementById('input-add-value'),
                    addNote: document.getElementById('input-add-note'),

                    // Бутони за действия
                    addButton: document.getElementById('button-add'),
                    removeSelectedButton: document.getElementById('button-remove-selected'),
                    exportButton: document.getElementById('button-export'),
                    importButton: document.getElementById('button-import'),
                    importFileInput: document.getElementById('import-file'),

                    // Checkboxes
                    selectAllCheckbox: document.querySelector('input[onclick*="selected"]'),
                    blacklistCheckboxes: document.querySelectorAll('input[name*="selected"]'),

                    // Remove buttons
                    removeButtons: document.querySelectorAll('.btn-remove-blacklist'),

                    // Модали
                    loadingOverlay: document.getElementById('loading'),
                    confirmModal: document.getElementById('confirm-modal'),
                    confirmModalTitle: document.getElementById('confirm-modal-title'),
                    confirmModalMessage: document.getElementById('confirm-modal-message'),
                    confirmModalConfirm: document.getElementById('confirm-modal-confirm'),
                    confirmModalCancel: document.getElementById('confirm-modal-cancel')
                };
            },

            // Свързване на събития
            bindBlacklistEvents: function() {
                const self = this;

                // Добавяне на нов запис - показване на формата
                if (self.fakeDetectorBlacklist.elements.addButton) {
                    self.fakeDetectorBlacklist.elements.addButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.showAddForm();
                    });
                }

                // Бутон за отказ от добавяне
                const cancelAddButton = document.getElementById('button-cancel-add');
                if (cancelAddButton) {
                    cancelAddButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.hideAddForm();
                    });
                }

                // Форма за добавяне - submit
                if (self.fakeDetectorBlacklist.elements.addFormElement) {
                    self.fakeDetectorBlacklist.elements.addFormElement.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.addBlacklistItem();
                    });
                }

                // Модален прозорец за потвърждение
                if (self.fakeDetectorBlacklist.elements.confirmModalCancel) {
                    self.fakeDetectorBlacklist.elements.confirmModalCancel.addEventListener('click', function() {
                        self.hideConfirmModal();
                    });
                }

                // Премахване на избрани записи
                if (self.fakeDetectorBlacklist.elements.removeSelectedButton) {
                    self.fakeDetectorBlacklist.elements.removeSelectedButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.removeSelectedBlacklistItems();
                    });
                }

                // Експорт
                if (self.fakeDetectorBlacklist.elements.exportButton) {
                    self.fakeDetectorBlacklist.elements.exportButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.exportBlacklistData();
                    });
                }

                // Импорт
                if (self.fakeDetectorBlacklist.elements.importFileInput) {
                    self.fakeDetectorBlacklist.elements.importFileInput.addEventListener('change', function(e) {
                        if (e.target.files.length > 0) {
                            self.importBlacklistData(e.target.files[0]);
                        }
                    });
                }

                // Select all checkbox
                if (self.fakeDetectorBlacklist.elements.selectAllCheckbox) {
                    self.fakeDetectorBlacklist.elements.selectAllCheckbox.addEventListener('change', function() {
                        self.toggleAllBlacklistSelection(this.checked);
                    });
                }

                // Individual checkboxes
                self.fakeDetectorBlacklist.elements.blacklistCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        self.updateBlacklistBulkActionButtons();
                    });
                });

                // Individual remove buttons
                self.fakeDetectorBlacklist.elements.removeButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const itemId = this.getAttribute('data-id');
                        self.removeBlacklistItem(itemId);
                    });
                });

                // Валидация на полетата в реално време
                if (self.fakeDetectorBlacklist.elements.addValue) {
                    self.fakeDetectorBlacklist.elements.addValue.addEventListener('input', function() {
                        self.validateBlacklistValue();
                    });
                }

                if (self.fakeDetectorBlacklist.elements.addType) {
                    self.fakeDetectorBlacklist.elements.addType.addEventListener('change', function() {
                        self.validateBlacklistValue();
                    });
                }
            },

            // Инициализация на компоненти
            initBlacklistComponents: function() {
                this.updateBlacklistBulkActionButtons();
                this.initBlacklistDatePickers();
            },

            // Добавяне на нов запис в черния списък
            addBlacklistItem: function() {
                const self = this;
                const elements = self.fakeDetectorBlacklist.elements;

                const type = elements.addType ? elements.addType.value : '';
                const value = elements.addValue ? elements.addValue.value.trim() : '';
                const note = elements.addNote ? elements.addNote.value.trim() : '';

                // Валидация
                if (!type || !value) {
                    self.showAlert('warning', 'Моля попълнете тип и стойност');
                    return;
                }

                if (!self.validateBlacklistValueFormat(type, value)) {
                    return;
                }

                // AJAX заявка за добавяне
                self.showBlacklistLoading('Добавяне на запис...');

                fetch(self.fakeDetectorBlacklist.config.addUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        type: type,
                        value: value,
                        note: note,
                        user_token: self.config.userToken
                    })
                })
                .then(response => response.json())
                .then(data => {
                    self.hideBlacklistLoading();

                    if (data.success) {
                        self.showAlert('success', data.message || 'Записът е добавен успешно');

                        // Скриване на формата
                        self.hideAddForm();

                        // Презареждане на страницата
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        self.showAlert('error', data.error || 'Възникна грешка при добавянето');
                    }
                })
                .catch(error => {
                    self.hideBlacklistLoading();
                    console.error('Add blacklist item error:', error);
                    self.showAlert('error', 'Възникна грешка при добавянето');
                });
            },

            // Премахване на избрани записи
            removeSelectedBlacklistItems: function() {
                const selectedIds = this.getSelectedBlacklistIds();

                if (selectedIds.length === 0) {
                    this.showAlert('warning', 'Моля изберете поне един запис');
                    return;
                }

                const self = this;
                const title = 'Премахване на записи';
                const message = `Сигурни ли сте, че искате да премахнете ${selectedIds.length} запис(а) от черния списък?`;

                self.showConfirmModal(title, message, function() {
                    self.performBlacklistBulkRemove(selectedIds);
                });
            },

            // Премахване на единичен запис
            removeBlacklistItem: function(itemId) {
                const self = this;
                const title = 'Премахване на запис';
                const message = 'Сигурни ли сте, че искате да премахнете този запис от черния списък?';

                self.showConfirmModal(title, message, function() {
                    self.performBlacklistBulkRemove([itemId]);
                });
            },

            // Получаване на избрани ID-та
            getSelectedBlacklistIds: function() {
                const selectedCheckboxes = document.querySelectorAll('input[name*="selected"]:checked');
                return Array.from(selectedCheckboxes).map(cb => cb.value);
            },

            // Изпълнение на масово премахване
            performBlacklistBulkRemove: function(ids) {
                const self = this;

                if (!self.fakeDetectorBlacklist.config.removeUrl) {
                    self.showAlert('error', 'Remove URL не е конфигуриран');
                    return;
                }

                self.showBlacklistLoading('Премахване на записи...');

                fetch(self.fakeDetectorBlacklist.config.removeUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        selected: ids,
                        user_token: self.config.userToken
                    })
                })
                .then(response => response.json())
                .then(data => {
                    self.hideBlacklistLoading();

                    if (data.success) {
                        self.showAlert('success', data.message || 'Записите са премахнати успешно');

                        // Презареждане на страницата
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        self.showAlert('error', data.error || 'Възникна грешка при премахването');
                    }
                })
                .catch(error => {
                    self.hideBlacklistLoading();
                    console.error('Remove blacklist items error:', error);
                    self.showAlert('error', 'Възникна грешка при премахването');
                });
            },

            // Toggle на всички checkboxes
            toggleAllBlacklistSelection: function(checked) {
                this.fakeDetectorBlacklist.elements.blacklistCheckboxes.forEach(checkbox => {
                    checkbox.checked = checked;
                });
                this.updateBlacklistBulkActionButtons();
            },

            // Обновяване на състоянието на bulk action бутоните
            updateBlacklistBulkActionButtons: function() {
                const selectedCount = this.getSelectedBlacklistIds().length;
                const hasSelection = selectedCount > 0;

                if (this.fakeDetectorBlacklist.elements.removeSelectedButton) {
                    this.fakeDetectorBlacklist.elements.removeSelectedButton.disabled = !hasSelection;
                    this.fakeDetectorBlacklist.elements.removeSelectedButton.textContent =
                        hasSelection ? `Премахни избраните (${selectedCount})` : 'Премахни избраните';
                }
            },

            // Валидация на стойността
            validateBlacklistValue: function() {
                const elements = this.fakeDetectorBlacklist.elements;
                const type = elements.addType ? elements.addType.value : '';
                const value = elements.addValue ? elements.addValue.value.trim() : '';

                if (!type || !value) return true;

                return this.validateBlacklistValueFormat(type, value);
            },

            // Валидация на формата на стойността
            validateBlacklistValueFormat: function(type, value) {
                const validation = this.fakeDetectorBlacklist.validation;

                if (type === 'ip') {
                    if (!validation.ip.test(value)) {
                        this.showAlert('error', 'Невалиден IP адрес формат');
                        return false;
                    }
                } else if (type === 'email') {
                    if (!validation.domain.test(value)) {
                        this.showAlert('error', 'Невалиден домейн формат');
                        return false;
                    }
                }

                return true;
            },

            // Експорт на данни
            exportBlacklistData: function() {
                const self = this;

                if (!self.fakeDetectorBlacklist.config.exportUrl) {
                    self.showAlert('error', 'Export URL не е конфигуриран');
                    return;
                }

                // Създаване на скрит iframe за download
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = self.fakeDetectorBlacklist.config.exportUrl + '&user_token=' + self.config.userToken;
                document.body.appendChild(iframe);

                // Премахване на iframe след 5 секунди
                setTimeout(() => {
                    document.body.removeChild(iframe);
                }, 5000);

                self.showAlert('info', 'Експортът започна. Файлът ще бъде изтеглен автоматично.');
            },

            // Импорт на данни
            importBlacklistData: function(file) {
                const self = this;

                if (!self.fakeDetectorBlacklist.config.importUrl) {
                    self.showAlert('error', 'Import URL не е конфигуриран');
                    return;
                }

                const formData = new FormData();
                formData.append('blacklist_file', file);
                formData.append('user_token', self.config.userToken);

                self.showBlacklistLoading('Импортиране на данни...');

                fetch(self.fakeDetectorBlacklist.config.importUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    self.hideBlacklistLoading();

                    if (data.success) {
                        self.showAlert('success', data.message || 'Данните са импортирани успешно');

                        // Презареждане на страницата
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        self.showAlert('error', data.error || 'Възникна грешка при импортирането');
                    }
                })
                .catch(error => {
                    self.hideBlacklistLoading();
                    console.error('Import blacklist data error:', error);
                    self.showAlert('error', 'Възникна грешка при импортирането');
                });
            },

            // Инициализация на date pickers
            initBlacklistDatePickers: function() {
                const dateInputs = document.querySelectorAll('#filter-date-from, #filter-date-to');
                dateInputs.forEach(input => {
                    if (input) {
                        input.type = 'date';
                    }
                });
            },

            // Показване на loading overlay
            showBlacklistLoading: function(message = 'Зареждане...') {
                const overlay = this.fakeDetectorBlacklist.elements.loadingOverlay;
                if (overlay) {
                    const messageElement = overlay.querySelector('span');
                    if (messageElement) {
                        messageElement.textContent = message;
                    }
                    overlay.classList.remove('hidden');
                }
            },

            // Скриване на loading overlay
            hideBlacklistLoading: function() {
                const overlay = this.fakeDetectorBlacklist.elements.loadingOverlay;
                if (overlay) {
                    overlay.classList.add('hidden');
                }
            },

            /**
             * Debug логване
             */
            log: function(message, data) {
                if (window.console && window.console.log) {
                    console.log('[FakeDetectorModule] ' + message, data || '');
                }
            },

            // Показване на формата за добавяне
            showAddForm: function() {
                const self = this;
                if (self.fakeDetectorBlacklist.elements.addForm) {
                    self.fakeDetectorBlacklist.elements.addForm.style.display = 'block';

                    // Фокус върху първото поле
                    if (self.fakeDetectorBlacklist.elements.addType) {
                        self.fakeDetectorBlacklist.elements.addType.focus();
                    }
                }
            },

            // Скриване на формата за добавяне
            hideAddForm: function() {
                const self = this;
                if (self.fakeDetectorBlacklist.elements.addForm) {
                    self.fakeDetectorBlacklist.elements.addForm.style.display = 'none';

                    // Изчистване на полетата
                    if (self.fakeDetectorBlacklist.elements.addFormElement) {
                        self.fakeDetectorBlacklist.elements.addFormElement.reset();
                    }
                }
            },

            // Показване на модален прозорец за потвърждение
            showConfirmModal: function(title, message, callback) {
                const self = this;
                const elements = self.fakeDetectorBlacklist.elements;

                if (!elements.confirmModal || !elements.confirmModalTitle || !elements.confirmModalMessage || !elements.confirmModalConfirm) {
                    // Fallback към стандартен confirm ако модалният прозорец не съществува
                    if (confirm(message)) {
                        callback();
                    }
                    return;
                }

                // Задаване на съдържание
                elements.confirmModalTitle.textContent = title;
                elements.confirmModalMessage.textContent = message;

                // Показване на модалния прозорец
                elements.confirmModal.classList.remove('hidden');

                // Добавяне на event listener за бутона за потвърждение
                const confirmHandler = function() {
                    self.hideConfirmModal();
                    callback();
                    elements.confirmModalConfirm.removeEventListener('click', confirmHandler);
                };

                elements.confirmModalConfirm.addEventListener('click', confirmHandler);
            },

            // Скриване на модалния прозорец за потвърждение
            hideConfirmModal: function() {
                const elements = this.fakeDetectorBlacklist.elements;
                if (elements.confirmModal) {
                    elements.confirmModal.classList.add('hidden');
                }
            }

        });
    }

})();