/**
 * Fake Detector Blacklist Module JavaScript
 * Специализиран модул за управление на черния списък
 */

(function() {
    'use strict';

    // Модул за черен списък
    const BlacklistModule = Object.assign({}, window.FakeDetectorModule || {}, {
        
        // Специфична конфигурация за черния списък
        blacklistConfig: {
            selectors: {
                addForm: '#form-add-blacklist',
                addFormContainer: '#add-form',
                addButton: '#button-add',
                cancelButton: '#button-cancel-add',
                removeButtons: '.btn-remove-blacklist',
                bulkRemoveButton: '#button-remove-selected',
                typeSelect: '#input-add-type',
                valueInput: '#input-add-value',
                noteInput: '#input-add-note'
            },
            validation: {
                ip: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
                domain: /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/
            }
        },

        // Инициализация на blacklist модула
        initBlacklist: function() {
            this.bindBlacklistEvents();
            this.initAutocomplete();
            this.log('Blacklist module initialized');
        },

        // Свързване на събития за черния списък
        bindBlacklistEvents: function() {
            const self = this;

            // Показване на формата за добавяне
            $(document).on('click', this.blacklistConfig.selectors.addButton, function(e) {
                e.preventDefault();
                self.showAddForm();
            });

            // Скриване на формата за добавяне
            $(document).on('click', this.blacklistConfig.selectors.cancelButton, function(e) {
                e.preventDefault();
                self.hideAddForm();
            });

            // Изпращане на формата за добавяне
            $(document).on('submit', this.blacklistConfig.selectors.addForm, function(e) {
                e.preventDefault();
                self.addToBlacklist();
            });

            // Премахване на единичен запис
            $(document).on('click', '.btn-remove-blacklist', function(e) {
                e.preventDefault();
                const id = $(this).data('id');
                self.removeFromBlacklist(id);
            });

            // Масово премахване
            $(document).on('click', this.blacklistConfig.selectors.bulkRemoveButton, function(e) {
                e.preventDefault();
                self.bulkRemoveFromBlacklist();
            });

            // Валидация при промяна на типа
            $(document).on('change', this.blacklistConfig.selectors.typeSelect, function() {
                self.updateValuePlaceholder();
                self.clearValueInput();
            });

            // Валидация в реално време
            $(document).on('input', this.blacklistConfig.selectors.valueInput, function() {
                self.validateValue();
            });
        },

        // Показване на формата за добавяне
        showAddForm: function() {
            $(this.blacklistConfig.selectors.addFormContainer).slideDown();
            $(this.blacklistConfig.selectors.typeSelect).focus();
        },

        // Скриване на формата за добавяне
        hideAddForm: function() {
            $(this.blacklistConfig.selectors.addFormContainer).slideUp();
            this.clearAddForm();
        },

        // Изчистване на формата за добавяне
        clearAddForm: function() {
            $(this.blacklistConfig.selectors.addForm)[0].reset();
            $(this.blacklistConfig.selectors.valueInput).removeClass('is-invalid is-valid');
        },

        // Обновяване на placeholder според типа
        updateValuePlaceholder: function() {
            const type = $(this.blacklistConfig.selectors.typeSelect).val();
            const valueInput = $(this.blacklistConfig.selectors.valueInput);
            
            if (type === 'ip') {
                valueInput.attr('placeholder', 'Например: ***********');
            } else if (type === 'email') {
                valueInput.attr('placeholder', 'Например: example.com');
            } else {
                valueInput.attr('placeholder', 'IP адрес или email домейн');
            }
        },

        // Изчистване на полето за стойност
        clearValueInput: function() {
            $(this.blacklistConfig.selectors.valueInput).val('').removeClass('is-invalid is-valid');
        },

        // Валидация на стойността
        validateValue: function() {
            const type = $(this.blacklistConfig.selectors.typeSelect).val();
            const value = $(this.blacklistConfig.selectors.valueInput).val().trim();
            const valueInput = $(this.blacklistConfig.selectors.valueInput);
            
            if (!value) {
                valueInput.removeClass('is-invalid is-valid');
                return null;
            }

            let isValid = false;
            
            if (type === 'ip') {
                isValid = this.blacklistConfig.validation.ip.test(value);
            } else if (type === 'email') {
                isValid = this.blacklistConfig.validation.domain.test(value);
            }

            if (isValid) {
                valueInput.removeClass('is-invalid').addClass('is-valid');
            } else {
                valueInput.removeClass('is-valid').addClass('is-invalid');
            }

            return isValid;
        },

        // Добавяне в черния списък
        addToBlacklist: function() {
            const type = $(this.blacklistConfig.selectors.typeSelect).val();
            const value = $(this.blacklistConfig.selectors.valueInput).val().trim();
            const note = $(this.blacklistConfig.selectors.noteInput).val().trim();

            // Валидация
            if (!type) {
                this.showAlert('Моля изберете тип!', 'warning');
                return;
            }

            if (!value) {
                this.showAlert('Моля въведете стойност!', 'warning');
                return;
            }

            if (!this.validateValue()) {
                this.showAlert('Моля въведете валидна стойност!', 'warning');
                return;
            }

            // Изпращане на заявката
            this.sendAjaxRequest('customer/fakedetector/blacklist', {
                action: 'add',
                type: type,
                value: value,
                note: note
            }, (response) => {
                if (response.success) {
                    this.showAlert(response.success, 'success');
                    if (response.reload) {
                        setTimeout(() => window.location.reload(), 1000);
                    }
                    this.hideAddForm();
                }
            });
        },

        // Премахване от черния списък
        removeFromBlacklist: function(id) {
            if (!confirm('Сигурни ли сте, че искате да премахнете този запис от черния списък?')) {
                return;
            }

            this.sendAjaxRequest('customer/fakedetector/blacklist', {
                action: 'remove',
                id: id
            }, (response) => {
                if (response.success) {
                    this.showAlert(response.success, 'success');
                    if (response.reload) {
                        setTimeout(() => window.location.reload(), 1000);
                    }
                }
            });
        },

        // Масово премахване от черния списък
        bulkRemoveFromBlacklist: function() {
            const selected = [];
            $('input[name*="selected"]:checked').each(function() {
                selected.push($(this).val());
            });

            if (selected.length === 0) {
                this.showAlert('Моля изберете записи за премахване!', 'warning');
                return;
            }

            if (!confirm(`Сигурни ли сте, че искате да премахнете ${selected.length} записа от черния списък?`)) {
                return;
            }

            this.sendAjaxRequest('customer/fakedetector/blacklist', {
                action: 'bulk_remove',
                ids: selected
            }, (response) => {
                if (response.success) {
                    this.showAlert(response.success, 'success');
                    if (response.reload) {
                        setTimeout(() => window.location.reload(), 1500);
                    }
                }
            });
        },

        // Инициализация на автодопълване
        initAutocomplete: function() {
            const self = this;
            
            $(this.blacklistConfig.selectors.valueInput).autocomplete({
                source: function(request, response) {
                    const type = $(self.blacklistConfig.selectors.typeSelect).val();
                    
                    if (!type) {
                        response([]);
                        return;
                    }

                    $.ajax({
                        url: 'customer/fakedetector/blacklist/autocomplete',
                        data: {
                            type: type,
                            q: request.term
                        },
                        success: function(data) {
                            response(data);
                        }
                    });
                },
                minLength: 2,
                delay: 300,
                select: function(event, ui) {
                    $(self.blacklistConfig.selectors.valueInput).val(ui.item.value);
                    self.validateValue();
                }
            });
        },

        // Експорт на черния списък
        exportBlacklist: function(format = 'csv') {
            const filters = this.getFilterValues();
            const params = new URLSearchParams(filters);
            params.append('format', format);
            
            window.open('customer/fakedetector/export?' + params.toString(), '_blank');
        },

        // Импорт на черния списък
        importBlacklist: function() {
            const modal = this.createModal('Импорт на черен списък', `
                <form id="import-form" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="import-file">Изберете файл:</label>
                        <input type="file" id="import-file" name="file" class="form-control" accept=".csv,.txt" required>
                        <small class="help-block">Поддържани формати: CSV, TXT</small>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="overwrite" value="1"> Презапиши съществуващи записи
                        </label>
                    </div>
                </form>
            `);

            // Добавяне на бутон за импорт
            modal.find('.modal-footer').prepend(`
                <button type="button" class="btn btn-primary" id="button-import">
                    <i class="fa fa-upload"></i> Импортирай
                </button>
            `);

            // Обработка на импорта
            modal.on('click', '#button-import', () => {
                const formData = new FormData($('#import-form')[0]);
                
                $.ajax({
                    url: 'customer/fakedetector/import',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: (response) => {
                        modal.modal('hide');
                        if (response.success) {
                            this.showAlert(response.success, 'success');
                            setTimeout(() => window.location.reload(), 1500);
                        }
                    }
                });
            });

            modal.modal('show');
        },

        // Показване на статистики за черния списък
        showBlacklistStats: function() {
            this.sendAjaxRequest('customer/fakedetector/blacklist/stats', {}, (response) => {
                if (response.stats) {
                    const stats = response.stats;
                    const content = `
                        <div class="row">
                            <div class="col-sm-6">
                                <table class="table table-striped">
                                    <tr><td>Общо записи:</td><td><strong>${stats.total || 0}</strong></td></tr>
                                    <tr><td>IP адреси:</td><td><strong>${stats.ip_count || 0}</strong></td></tr>
                                    <tr><td>Email домейни:</td><td><strong>${stats.email_count || 0}</strong></td></tr>
                                </table>
                            </div>
                            <div class="col-sm-6">
                                <table class="table table-striped">
                                    <tr><td>Засегнати потребители:</td><td><strong>${stats.affected_customers || 0}</strong></td></tr>
                                    <tr><td>Последно добавяне:</td><td><strong>${stats.last_added || 'Няма'}</strong></td></tr>
                                </table>
                            </div>
                        </div>
                    `;
                    
                    const modal = this.createModal('Статистики за черния списък', content);
                    modal.modal('show');
                }
            });
        }
    });

    // Инициализация при зареждане на DOM
    $(document).ready(function() {
        if (window.location.pathname.includes('blacklist')) {
            BlacklistModule.initBlacklist();
        }
    });

    // Експортиране на модула
    window.BlacklistModule = BlacklistModule;

})();
