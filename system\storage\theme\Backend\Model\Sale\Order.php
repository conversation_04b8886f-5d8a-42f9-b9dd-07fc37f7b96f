<?php

namespace Theme25\Backend\Model\Sale;

// Включване на стандартния модел
require_once(DIR_APPLICATION . 'model/sale/order.php');

class Order extends \ModelSaleOrder {

    /**
     * Получаване на всички поръчки за даден клиент
     * Разширяваме функционалността на родителския клас
     * 
     * @param int $customer_id ID на клиента
     * @return array Масив с поръчки
     */
    public function getOrdersByCustomerId($customer_id) {
        $query = $this->db->query("
            SELECT 
                o.order_id,
                o.customer_id,
                o.customer_group_id,
                o.firstname,
                o.lastname,
                o.email,
                o.telephone,
                o.order_status_id,
                o.total,
                o.currency_code,
                o.currency_value,
                o.date_added,
                o.date_modified,
                os.name as order_status
            FROM `" . DB_PREFIX . "order` o
            LEFT JOIN `" . DB_PREFIX . "order_status` os ON (o.order_status_id = os.order_status_id)
            WHERE o.customer_id = '" . (int)$customer_id . "'
            AND os.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ORDER BY o.date_added DESC
        ");

        return $query->rows;
    }

    /**
     * Получаване на броя поръчки за даден клиент
     * 
     * @param int $customer_id ID на клиента
     * @return int Брой поръчки
     */
    public function getTotalOrdersByCustomerId($customer_id) {
        $query = $this->db->query("
            SELECT COUNT(*) as total 
            FROM `" . DB_PREFIX . "order` 
            WHERE customer_id = '" . (int)$customer_id . "'
        ");

        return (int)$query->row['total'];
    }

    /**
     * Получаване на активни поръчки за даден клиент
     * (поръчки, които не са завършени или отказани)
     * 
     * @param int $customer_id ID на клиента
     * @return array Масив с активни поръчки
     */
    public function getActiveOrdersByCustomerId($customer_id) {
        // Статуси, които се считат за активни (не завършени/отказани)
        $active_statuses = [1, 2, 3, 15]; // Pending, Processing, Shipped, Processing
        
        $query = $this->db->query("
            SELECT 
                o.order_id,
                o.customer_id,
                o.firstname,
                o.lastname,
                o.email,
                o.order_status_id,
                o.total,
                o.currency_code,
                o.date_added,
                os.name as order_status
            FROM `" . DB_PREFIX . "order` o
            LEFT JOIN `" . DB_PREFIX . "order_status` os ON (o.order_status_id = os.order_status_id)
            WHERE o.customer_id = '" . (int)$customer_id . "'
            AND o.order_status_id IN (" . implode(',', array_map('intval', $active_statuses)) . ")
            AND os.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ORDER BY o.date_added DESC
        ");

        return $query->rows;
    }

    /**
     * Получаване на общата сума на поръчките за даден клиент
     * 
     * @param int $customer_id ID на клиента
     * @param array $status_ids Масив със статуси на поръчки (по подразбиране всички)
     * @return float Обща сума
     */
    public function getTotalOrderValueByCustomerId($customer_id, $status_ids = []) {
        $sql = "
            SELECT SUM(total) as total_value 
            FROM `" . DB_PREFIX . "order` 
            WHERE customer_id = '" . (int)$customer_id . "'
        ";
        
        if (!empty($status_ids)) {
            $sql .= " AND order_status_id IN (" . implode(',', array_map('intval', $status_ids)) . ")";
        }

        $query = $this->db->query($sql);

        return (float)$query->row['total_value'];
    }

    /**
     * Получаване на последната поръчка за даден клиент
     * 
     * @param int $customer_id ID на клиента
     * @return array|null Данни за последната поръчка или null
     */
    public function getLastOrderByCustomerId($customer_id) {
        $query = $this->db->query("
            SELECT 
                o.order_id,
                o.customer_id,
                o.firstname,
                o.lastname,
                o.email,
                o.order_status_id,
                o.total,
                o.currency_code,
                o.date_added,
                os.name as order_status
            FROM `" . DB_PREFIX . "order` o
            LEFT JOIN `" . DB_PREFIX . "order_status` os ON (o.order_status_id = os.order_status_id)
            WHERE o.customer_id = '" . (int)$customer_id . "'
            AND os.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ORDER BY o.date_added DESC
            LIMIT 1
        ");

        return $query->num_rows ? $query->row : null;
    }

    /**
     * Проверява дали клиент има поръчки
     * 
     * @param int $customer_id ID на клиента
     * @return bool True ако има поръчки, false ако няма
     */
    public function hasOrdersByCustomerId($customer_id) {
        $query = $this->db->query("
            SELECT COUNT(*) as total 
            FROM `" . DB_PREFIX . "order` 
            WHERE customer_id = '" . (int)$customer_id . "'
            LIMIT 1
        ");

        return (int)$query->row['total'] > 0;
    }

    /**
     * Получаване на статистики за поръчките на клиент
     * 
     * @param int $customer_id ID на клиента
     * @return array Статистики
     */
    public function getOrderStatsByCustomerId($customer_id) {
        $total_orders = $this->getTotalOrdersByCustomerId($customer_id);
        $total_value = $this->getTotalOrderValueByCustomerId($customer_id);
        $active_orders = count($this->getActiveOrdersByCustomerId($customer_id));
        $last_order = $this->getLastOrderByCustomerId($customer_id);

        return [
            'total_orders' => $total_orders,
            'total_value' => $total_value,
            'active_orders' => $active_orders,
            'last_order_date' => $last_order ? $last_order['date_added'] : null,
            'last_order_id' => $last_order ? $last_order['order_id'] : null,
            'average_order_value' => $total_orders > 0 ? ($total_value / $total_orders) : 0
        ];
    }

    /**
     * Получаване на поръчки за даден клиент с пагинация
     * 
     * @param int $customer_id ID на клиента
     * @param array $data Параметри за филтриране и пагинация
     * @return array Масив с поръчки
     */
    public function getOrdersByCustomerIdPaginated($customer_id, $data = []) {
        $sql = "
            SELECT 
                o.order_id,
                o.customer_id,
                o.firstname,
                o.lastname,
                o.email,
                o.telephone,
                o.order_status_id,
                o.total,
                o.currency_code,
                o.currency_value,
                o.date_added,
                o.date_modified,
                os.name as order_status
            FROM `" . DB_PREFIX . "order` o
            LEFT JOIN `" . DB_PREFIX . "order_status` os ON (o.order_status_id = os.order_status_id)
            WHERE o.customer_id = '" . (int)$customer_id . "'
            AND os.language_id = '" . (int)$this->config->get('config_language_id') . "'
        ";

        // Филтриране по статус
        if (isset($data['filter_order_status_id']) && $data['filter_order_status_id'] !== '') {
            $sql .= " AND o.order_status_id = '" . (int)$data['filter_order_status_id'] . "'";
        }

        // Сортиране
        $sort_data = [
            'o.order_id',
            'o.firstname',
            'o.lastname',
            'o.email',
            'o.total',
            'o.date_added',
            'order_status'
        ];

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY o.date_added";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        // Пагинация
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }
}
