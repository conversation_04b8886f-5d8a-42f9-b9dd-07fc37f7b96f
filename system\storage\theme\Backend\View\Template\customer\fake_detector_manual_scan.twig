{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="button-test-scan" class="btn btn-info" data-toggle="tooltip" title="Тестово сканиране">
          <i class="fa fa-eye"></i> Тест
        </button>
        <button type="button" id="button-run-scan" class="btn btn-primary" data-toggle="tooltip" title="Изпълни сканиране">
          <i class="fa fa-play"></i> Изпълни
        </button>
        <a href="{{ back_url }}" class="btn btn-default" data-toggle="tooltip" title="Назад">
          <i class="fa fa-reply"></i> Назад
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <form id="form-manual-scan" method="post" action="{{ action_url }}">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-cogs"></i> Настройки за ръчно сканиране</h3>
        </div>
        <div class="panel-body">
          
          <!-- Основни настройки -->
          <div class="row">
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label">
                  <input type="checkbox" name="test_mode" value="1" checked /> Тестов режим
                </label>
                <div class="help-block">В тестов режим няма да се запазват промени в базата данни.</div>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label" for="input-date-range">Период за сканиране</label>
                <div class="input-group">
                  <input type="text" name="date_from" placeholder="От дата" id="input-date-from" class="form-control" />
                  <span class="input-group-addon">до</span>
                  <input type="text" name="date_to" placeholder="До дата" id="input-date-to" class="form-control" />
                </div>
                <div class="help-block">Оставете празно за сканиране на всички потребители.</div>
              </div>
            </div>
          </div>

          <!-- Правила за сканиране -->
          <div class="form-group">
            <label class="control-label">Правила за сканиране</label>
            <div class="well">
              {% for rule_key, rule_name in available_rules %}
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="rules[]" value="{{ rule_key }}" checked /> {{ rule_name }}
                </label>
              </div>
              {% endfor %}
            </div>
          </div>

          <!-- Специфични потребители -->
          <div class="form-group">
            <label class="control-label" for="input-customer-ids">Специфични потребители (опционално)</label>
            <textarea name="customer_ids" id="input-customer-ids" class="form-control" rows="3" placeholder="Въведете ID-та на потребители, разделени със запетая (напр. 1,2,3)"></textarea>
            <div class="help-block">Оставете празно за сканиране на всички потребители в зададения период.</div>
          </div>

        </div>
      </div>

      <!-- Статистики -->
      <div class="panel panel-info">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-bar-chart"></i> Статистики</h3>
        </div>
        <div class="panel-body">
          <div class="row">
            <div class="col-sm-3">
              <div class="info-box">
                <div class="info-box-icon bg-blue">
                  <i class="fa fa-users"></i>
                </div>
                <div class="info-box-content">
                  <span class="info-box-text">Общо потребители</span>
                  <span class="info-box-number">{{ customer_stats.total_customers }}</span>
                </div>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="info-box">
                <div class="info-box-icon bg-yellow">
                  <i class="fa fa-flag"></i>
                </div>
                <div class="info-box-content">
                  <span class="info-box-text">Маркирани</span>
                  <span class="info-box-number">{{ customer_stats.flagged_customers }}</span>
                </div>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="info-box">
                <div class="info-box-icon bg-green">
                  <i class="fa fa-check"></i>
                </div>
                <div class="info-box-content">
                  <span class="info-box-text">Одобрени</span>
                  <span class="info-box-number">{{ customer_stats.approved_customers }}</span>
                </div>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="info-box">
                <div class="info-box-icon bg-red">
                  <i class="fa fa-trash"></i>
                </div>
                <div class="info-box-content">
                  <span class="info-box-text">Изтрити</span>
                  <span class="info-box-number">{{ customer_stats.deleted_customers }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Текущи настройки -->
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-cog"></i> Текущи настройки на детектора</h3>
        </div>
        <div class="panel-body">
          <div class="row">
            <div class="col-sm-6">
              <table class="table table-striped">
                <tr>
                  <td>Минимална дължина на име:</td>
                  <td><code>{{ settings.min_username_length }}</code></td>
                </tr>
                <tr>
                  <td>Regex за имена:</td>
                  <td><code>{{ settings.regex_username }}</code></td>
                </tr>
                <tr>
                  <td>Максимум акаунти на IP:</td>
                  <td><code>{{ settings.max_accounts_per_ip }}</code></td>
                </tr>
                <tr>
                  <td>Само без поръчки:</td>
                  <td>
                    {% if settings.only_no_orders %}
                    <span class="label label-success">Да</span>
                    {% else %}
                    <span class="label label-default">Не</span>
                    {% endif %}
                  </td>
                </tr>
              </table>
            </div>
            <div class="col-sm-6">
              <table class="table table-striped">
                <tr>
                  <td>Проверка в черен списък:</td>
                  <td>
                    {% if settings.check_blacklist %}
                    <span class="label label-success">Да</span>
                    {% else %}
                    <span class="label label-default">Не</span>
                    {% endif %}
                  </td>
                </tr>
                <tr>
                  <td>Съмнителни домейни:</td>
                  <td>
                    {% if settings.bad_domains %}
                    <span class="badge">{{ settings.bad_domains|length }}</span> домейна
                    {% else %}
                    <span class="text-muted">Няма</span>
                    {% endif %}
                  </td>
                </tr>
                <tr>
                  <td>Автоматично сканиране:</td>
                  <td>
                    {% if settings.auto_scan_enabled %}
                    <span class="label label-success">Активно</span>
                    {% else %}
                    <span class="label label-default">Неактивно</span>
                    {% endif %}
                  </td>
                </tr>
                <tr>
                  <td>Интервал на сканиране:</td>
                  <td><code>{{ settings.scan_interval_hours }}</code> часа</td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>

    </form>

    <!-- Резултати от тестово сканиране -->
    <div id="test-results" class="panel panel-warning" style="display: none;">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-eye"></i> Резултати от тестово сканиране</h3>
      </div>
      <div class="panel-body">
        <div id="test-results-content">
          <!-- Съдържанието ще се зареди динамично -->
        </div>
      </div>
    </div>

    <!-- Прогрес бар -->
    <div id="scan-progress" class="panel panel-info" style="display: none;">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-spinner fa-spin"></i> Изпълнение на сканиране...</h3>
      </div>
      <div class="panel-body">
        <div class="progress">
          <div id="progress-bar" class="progress-bar progress-bar-info" role="progressbar" style="width: 0%">
            <span id="progress-text">0%</span>
          </div>
        </div>
        <div id="scan-status">Подготовка за сканиране...</div>
      </div>
    </div>

  </div>
</div>

{{ footer }}
