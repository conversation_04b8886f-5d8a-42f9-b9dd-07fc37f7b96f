<!-- Manual <PERSON>an <PERSON> -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
  <div class="flex flex-col md:flex-row md:items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Ръчно сканиране</h1>
      <p class="text-gray-500 mt-1">Настройки и параметри за ръчно сканиране на потребители</p>
    </div>
    <div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
      <a href="{{ back_url }}" class="px-4 py-2 bg-gray-600 text-white rounded-button hover:bg-gray-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-arrow-left-line"></i>
        </div>
        <span>Назад</span>
      </a>
      <button type="button" id="button-run-scan" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-play-line"></i>
        </div>
        <span>Изпълни</span>
      </button>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="p-6 overflow-y-auto">
  <!-- Alert Messages -->
  {% if error_warning %}
  <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-error-warning-line text-red-500 mr-2"></i>
      <span class="text-red-700">{{ error_warning }}</span>
    </div>
  </div>
  {% endif %}

  {% if success %}
  <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-check-line text-green-500 mr-2"></i>
      <span class="text-green-700">{{ success }}</span>
    </div>
  </div>
  {% endif %}

  <!-- Scan Form Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 flex items-center">
        <i class="ri-settings-3-line mr-2"></i>
        Настройки за сканиране
      </h3>
    </div>
    <div class="p-6">
      <form id="form-manual-scan">
        <!-- Basic Settings -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label class="flex items-center">
              <input type="checkbox" name="test_mode" value="1" checked class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
              <span class="text-sm font-medium text-gray-700">Тестов режим</span>
            </label>
            <p class="text-xs text-gray-500 mt-1">В тестов режим няма да се запазват промени в базата данни.</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Период за сканиране</label>
            <div class="grid grid-cols-2 gap-2">
              <div class="relative">
                <input type="number" name="days_from" min="0" max="365" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
                <span class="text-xs text-gray-500 absolute right-2 top-1/2 transform -translate-y-1/2">дни</span>
              </div>
              <div class="relative">
                <input type="number" name="max_customers" min="1" max="10000" value="1000" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
                <span class="text-xs text-gray-500 absolute right-2 top-1/2 transform -translate-y-1/2">макс.</span>
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-1">Период и максимален брой потребители за сканиране.</p>
          </div>
        </div>

        <!-- Detection Rules -->
        <div class="mb-6">
          <h4 class="text-sm font-medium text-gray-700 mb-3">Правила за детекция</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for rule_key, rule_info in available_rules %}
            <div>
              <label class="flex items-center">
                <input type="checkbox" name="rules[]" value="{{ rule_key }}" checked class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
                <span class="text-sm font-medium text-gray-700">{{ rule_info.name }}</span>
              </label>
              <p class="text-xs text-gray-500 mt-1 ml-6">{{ rule_info.description }}</p>
            </div>
            {% endfor %}
          </div>
        </div>

        <!-- Specific Customers -->
        <div class="mb-6">
          <h4 class="text-sm font-medium text-gray-700 mb-3">Конкретни потребители (опционално)</h4>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-customer-ids">ID на потребители</label>
            <textarea id="input-customer-ids" name="customer_ids" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Въведете ID на потребители, разделени със запетая (напр. 123, 456, 789)"></textarea>
            <p class="text-xs text-gray-500 mt-1">Оставете празно за сканиране на всички потребители.</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-email-domains">Email домейни</label>
            <textarea id="input-email-domains" name="email_domains" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Въведете email домейни, разделени с нов ред (напр. example.com)"></textarea>
            <p class="text-xs text-gray-500 mt-1">Оставете празно за сканиране на всички домейни.</p>
          </div>
        </div>

        <!-- Advanced Options -->
        <div class="mb-6">
          <h4 class="text-sm font-medium text-gray-700 mb-3">Разширени опции</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="flex items-center">
                <input type="checkbox" name="only_no_orders" value="1" checked class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
                <span class="text-sm font-medium text-gray-700">Само потребители без поръчки</span>
              </label>
              <p class="text-xs text-gray-500 mt-1 ml-6">Сканиране само на потребители, които нямат поръчки.</p>
            </div>
            <div>
              <label class="flex items-center">
                <input type="checkbox" name="check_blacklist" value="1" checked class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
                <span class="text-sm font-medium text-gray-700">Проверка в черен списък</span>
              </label>
              <p class="text-xs text-gray-500 mt-1 ml-6">Проверка на IP и email домейни в черния списък.</p>
            </div>
          </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end">
          <button type="button" id="button-reset" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors mr-3">
            <i class="ri-refresh-line mr-1"></i>
            Изчисти
          </button>
          <button type="button" id="button-run-scan-bottom" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
            <i class="ri-play-line mr-1"></i>
            Изпълни сканиране
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Scan Progress Card -->
  <div id="scan-progress" class="bg-white rounded-lg shadow-sm border border-blue-200 mb-6" style="display: none;">
    <div class="px-6 py-4 border-b border-blue-200 bg-blue-50">
      <h3 class="text-lg font-medium text-blue-800 flex items-center">
        <i class="ri-loader-4-line mr-2 animate-spin"></i>
        Сканиране в процес
      </h3>
    </div>
    <div class="p-6">
      <div class="flex items-center justify-center py-4">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
      <p class="text-center text-gray-700 mb-4">Моля, изчакайте докато сканирането приключи...</p>
      <div class="w-full bg-gray-200 rounded-full h-2.5">
        <div id="progress-bar" class="bg-primary h-2.5 rounded-full" style="width: 0%"></div>
      </div>
      <p class="text-center text-sm text-gray-500 mt-2" id="progress-text">Подготовка...</p>
    </div>
  </div>

  <!-- Test Results Card -->
  <div id="test-results" class="bg-white rounded-lg shadow-sm border border-orange-200 mb-6" style="display: none;">
    <div class="px-6 py-4 border-b border-orange-200 bg-orange-50">
      <h3 class="text-lg font-medium text-orange-800 flex items-center">
        <i class="ri-eye-line mr-2"></i>
        Резултати от сканирането
      </h3>
    </div>
    <div class="p-6">
      <div id="test-results-content">
        <!-- Съдържанието ще се зареди динамично -->
      </div>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div id="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg p-6 flex items-center">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-3"></div>
    <span class="text-gray-700">Зареждане...</span>
  </div>
</div>

<!-- Confirm Modal -->
<div id="confirm-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-lg w-11/12 md:w-1/2 max-w-lg">
    <div class="p-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900" id="confirm-modal-title">Потвърждение</h3>
    </div>
    <div class="p-6">
      <p class="text-gray-700" id="confirm-modal-message">Сигурни ли сте, че искате да продължите?</p>
    </div>
    <div class="p-4 border-t border-gray-200 flex justify-end space-x-3">
      <button type="button" id="confirm-modal-cancel" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
        Отказ
      </button>
      <button type="button" id="confirm-modal-confirm" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
        Потвърди
      </button>
    </div>
  </div>
</div>

<script>
// Configuration for the manual scan page
window.manualScanConfig = {
  userToken: '{{ user_token }}',
  actionUrl: '{{ action_url }}',
  testUrl: '{{ test_url }}',
  backUrl: '{{ back_url }}',
  availableRules: {{ available_rules|json_encode|raw }}
};
</script>
