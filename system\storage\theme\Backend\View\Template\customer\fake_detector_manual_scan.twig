<!-- Manual Scan <PERSON> -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
  <div class="flex flex-col md:flex-row md:items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Ръчно сканиране</h1>
      <p class="text-gray-500 mt-1">Персонализирано сканиране за откриване на фалшиви регистрации</p>
    </div>
    <div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
      <a href="{{ back_url }}" class="px-4 py-2 bg-gray-600 text-white rounded-button hover:bg-gray-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-arrow-left-line"></i>
        </div>
        <span>Назад</span>
      </a>
      <button type="button" id="button-test-scan" class="px-4 py-2 bg-blue-600 text-white rounded-button hover:bg-blue-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-eye-line"></i>
        </div>
        <span>Тест</span>
      </button>
      <button type="button" id="button-run-scan" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-play-line"></i>
        </div>
        <span>Изпълни</span>
      </button>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="p-6 overflow-y-auto">
  <!-- Alert Messages -->
  {% if error_warning %}
  <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-error-warning-line text-red-500 mr-2"></i>
      <span class="text-red-700">{{ error_warning }}</span>
    </div>
  </div>
  {% endif %}

  {% if success %}
  <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-check-line text-green-500 mr-2"></i>
      <span class="text-green-700">{{ success }}</span>
    </div>
  </div>
  {% endif %}

  <!-- Scan Configuration Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 flex items-center">
        <i class="ri-settings-3-line mr-2"></i>
        Настройки за ръчно сканиране
      </h3>
    </div>
    <div class="p-6">
      <form id="form-manual-scan" method="post" action="{{ action_url }}">

        <!-- Basic Settings -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label class="flex items-center">
              <input type="checkbox" name="test_mode" value="1" checked class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
              <span class="text-sm font-medium text-gray-700">Тестов режим</span>
            </label>
            <p class="text-xs text-gray-500 mt-1">В тестов режим няма да се запазват промени в базата данни.</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Период за сканиране</label>
            <div class="grid grid-cols-2 gap-2">
              <div class="relative">
                <input type="text" name="date_from" placeholder="От дата" id="input-date-from" class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <i class="ri-calendar-line text-gray-400"></i>
                </div>
              </div>
              <div class="relative">
                <input type="text" name="date_to" placeholder="До дата" id="input-date-to" class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <i class="ri-calendar-line text-gray-400"></i>
                </div>
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-1">Оставете празно за сканиране на всички потребители.</p>
          </div>
        </div>

        <!-- Scan Rules -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">Правила за сканиране</label>
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              {% for rule_key, rule_name in available_rules %}
              <label class="flex items-center">
                <input type="checkbox" name="rules[]" value="{{ rule_key }}" checked class="rounded border-gray-300 text-primary focus:ring-primary mr-2" />
                <span class="text-sm text-gray-700">{{ rule_name }}</span>
              </label>
              {% endfor %}
            </div>
          </div>
        </div>

        <!-- Specific Customers -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2" for="input-customer-ids">Специфични потребители (опционално)</label>
          <textarea name="customer_ids" id="input-customer-ids" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" rows="3" placeholder="Въведете ID-та на потребители, разделени със запетая (напр. 1,2,3)"></textarea>
          <p class="text-xs text-gray-500 mt-1">Оставете празно за сканиране на всички потребители в зададения период.</p>
        </div>

      </form>
    </div>
  </div>

  <!-- Statistics Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 flex items-center">
        <i class="ri-bar-chart-line mr-2"></i>
        Статистики
      </h3>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg border border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Общо потребители</p>
              <p class="text-2xl font-bold text-blue-600">{{ customer_stats.total_customers|default(0) }}</p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="ri-user-line text-blue-600 text-xl"></i>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Маркирани</p>
              <p class="text-2xl font-bold text-orange-600">{{ customer_stats.flagged_customers|default(0) }}</p>
            </div>
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <i class="ri-flag-line text-orange-600 text-xl"></i>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Одобрени</p>
              <p class="text-2xl font-bold text-green-600">{{ customer_stats.approved_customers|default(0) }}</p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="ri-check-line text-green-600 text-xl"></i>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Изтрити</p>
              <p class="text-2xl font-bold text-red-600">{{ customer_stats.deleted_customers|default(0) }}</p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <i class="ri-delete-bin-line text-red-600 text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Current Settings Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 flex items-center">
        <i class="ri-settings-line mr-2"></i>
        Текущи настройки на детектора
      </h3>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="text-sm font-medium text-gray-600">Минимална дължина на име:</span>
            <code class="px-2 py-1 bg-gray-100 rounded text-sm">{{ settings.min_username_length|default(10) }}</code>
          </div>
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="text-sm font-medium text-gray-600">Regex за имена:</span>
            <code class="px-2 py-1 bg-gray-100 rounded text-sm text-xs">{{ settings.regex_username|default('/^[a-zA-Z0-9]{10,}$/') }}</code>
          </div>
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="text-sm font-medium text-gray-600">Максимум акаунти на IP:</span>
            <code class="px-2 py-1 bg-gray-100 rounded text-sm">{{ settings.max_accounts_per_ip|default(3) }}</code>
          </div>
          <div class="flex justify-between items-center py-2">
            <span class="text-sm font-medium text-gray-600">Само без поръчки:</span>
            {% if settings.only_no_orders %}
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Да</span>
            {% else %}
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Не</span>
            {% endif %}
          </div>
        </div>

        <div class="space-y-4">
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="text-sm font-medium text-gray-600">Проверка в черен списък:</span>
            {% if settings.check_blacklist %}
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Да</span>
            {% else %}
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Не</span>
            {% endif %}
          </div>
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="text-sm font-medium text-gray-600">Съмнителни домейни:</span>
            {% if settings.bad_domains %}
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">{{ settings.bad_domains|length }} домейна</span>
            {% else %}
            <span class="text-gray-400">Няма</span>
            {% endif %}
          </div>
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="text-sm font-medium text-gray-600">Автоматично сканиране:</span>
            {% if settings.auto_scan_enabled %}
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Активно</span>
            {% else %}
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Неактивно</span>
            {% endif %}
          </div>
          <div class="flex justify-between items-center py-2">
            <span class="text-sm font-medium text-gray-600">Интервал на сканиране:</span>
            <code class="px-2 py-1 bg-gray-100 rounded text-sm">{{ settings.scan_interval_hours|default(24) }} часа</code>
          </div>
        </div>
      </div>
    </div>
  </div>

      </form>

  <!-- Test Results Card -->
  <div id="test-results" class="bg-white rounded-lg shadow-sm border border-orange-200 mb-6" style="display: none;">
    <div class="px-6 py-4 border-b border-orange-200 bg-orange-50">
      <h3 class="text-lg font-medium text-orange-800 flex items-center">
        <i class="ri-eye-line mr-2"></i>
        Резултати от тестово сканиране
      </h3>
    </div>
    <div class="p-6">
      <div id="test-results-content">
        <!-- Съдържанието ще се зареди динамично -->
      </div>
    </div>
  </div>

  <!-- Progress Card -->
  <div id="scan-progress" class="bg-white rounded-lg shadow-sm border border-blue-200 mb-6" style="display: none;">
    <div class="px-6 py-4 border-b border-blue-200 bg-blue-50">
      <h3 class="text-lg font-medium text-blue-800 flex items-center">
        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
        Изпълнение на сканиране...
      </h3>
    </div>
    <div class="p-6">
      <div class="w-full bg-gray-200 rounded-full h-4 mb-4">
        <div id="progress-bar" class="bg-blue-600 h-4 rounded-full transition-all duration-300" style="width: 0%">
          <span id="progress-text" class="sr-only">0%</span>
        </div>
      </div>
      <div class="flex justify-between items-center text-sm">
        <span id="scan-status" class="text-gray-600">Подготовка за сканиране...</span>
        <span id="progress-text-visible" class="font-medium text-blue-600">0%</span>
      </div>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div id="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg p-6 flex items-center">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-3"></div>
    <span class="text-gray-700">Зареждане...</span>
  </div>
</div>

<script>
// Configuration for the manual scan page
window.manualScanConfig = {
  userToken: '{{ user_token }}',
  actionUrl: '{{ action_url }}',
  testUrl: '{{ test_url }}',
  backUrl: '{{ back_url }}',
  availableRules: {{ available_rules|json_encode|raw }}
};
</script>
