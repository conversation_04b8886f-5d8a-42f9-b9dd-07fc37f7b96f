# 🎯 Customer Form Tabs Enhancement - Документация

## 📋 Обхват на промените

Обновен е Customer Edit формата в проекта Rakla.bg да включва всички табове от оригиналния OpenCart контролер **БЕЗ Affiliate секцията**:

- ✅ **History таб** - преглед и добавяне на история
- ✅ **Transaction таб** - управление на транзакции
- ✅ **Reward Points таб** - управление на точки
- ✅ **IP Address таб** - преглед на IP адреси

---

## 🎨 Frontend промени

### **1. Обновен Twig шаблон**
**Файл:** `system/storage/theme/Backend/View/Template/customer/customer_form.twig`

#### **Нови tab buttons:**
```html
<div class="flex overflow-x-auto">
    <button type="button" data-tab="tab-general" class="tab-button active">
        {{ tab_general|default('Основна информация') }}
    </button>
    <button type="button" data-tab="tab-addresses" class="tab-button">
        {{ tab_address|default('Адреси') }}
    </button>
    {% if customer_id %}
    <button type="button" data-tab="tab-history" class="tab-button">
        {{ tab_history|default('История') }}
    </button>
    <button type="button" data-tab="tab-transaction" class="tab-button">
        {{ tab_transaction|default('Транзакции') }}
    </button>
    <button type="button" data-tab="tab-reward" class="tab-button">
        {{ tab_reward|default('Reward Points') }}
    </button>
    <button type="button" data-tab="tab-ip" class="tab-button">
        {{ tab_ip|default('IP адреси') }}
    </button>
    {% endif %}
</div>
```

#### **History таб структура:**
```html
<div id="tab-history" class="tab-content hidden">
    <div class="p-6 space-y-6">
        <!-- History List -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">История на клиента</h3>
            <div id="history-list" class="space-y-3">
                <!-- History items loaded via AJAX -->
            </div>
        </div>

        <!-- Add History Form -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">Добавяне на коментар</h4>
            <textarea id="history-comment" rows="4" 
                      placeholder="Въведете коментар за историята на клиента..."></textarea>
            <button type="button" id="add-history-btn">
                <i class="ri-add-line mr-2"></i>Добави коментар
            </button>
        </div>
    </div>
</div>
```

#### **Transaction таб структура:**
```html
<div id="tab-transaction" class="tab-content hidden">
    <div class="p-6 space-y-6">
        <!-- Transaction List -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Транзакции</h3>
            <div id="transaction-list" class="space-y-3">
                <!-- Transaction items loaded via AJAX -->
            </div>
        </div>

        <!-- Add Transaction Form -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">Добавяне на транзакция</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input type="text" id="transaction-description" 
                       placeholder="Описание на транзакцията...">
                <input type="number" step="0.01" id="transaction-amount" 
                       placeholder="0.00">
            </div>
            <button type="button" id="add-transaction-btn">
                <i class="ri-add-line mr-2"></i>Добави транзакция
            </button>
        </div>
    </div>
</div>
```

#### **Reward Points таб структура:**
```html
<div id="tab-reward" class="tab-content hidden">
    <div class="p-6 space-y-6">
        <!-- Reward Points List -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Reward Points</h3>
            <div id="reward-list" class="space-y-3">
                <!-- Reward items loaded via AJAX -->
            </div>
        </div>

        <!-- Add Reward Points Form -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">Добавяне на reward points</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input type="text" id="reward-description" 
                       placeholder="Описание на reward points...">
                <input type="number" id="reward-points" 
                       placeholder="0">
            </div>
            <button type="button" id="add-reward-btn">
                <i class="ri-add-line mr-2"></i>Добави точки
            </button>
        </div>
    </div>
</div>
```

#### **IP Address таб структура:**
```html
<div id="tab-ip" class="tab-content hidden">
    <div class="p-6">
        <div class="bg-gray-50 rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">IP адреси</h3>
            <div id="ip-list" class="space-y-3">
                <!-- IP addresses loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
```

#### **Обновена JavaScript конфигурация:**
```html
<script>
window.customerFormConfig = {
    userToken: '{{ user_token }}',
    customerId: {{ customer_id|default(0) }},
    countries: {{ countries|json_encode|raw }},
    unlockUrl: '{{ unlock|default("") }}',
    historyUrl: '{{ history_url|default("") }}',
    addHistoryUrl: '{{ add_history_url|default("") }}',
    transactionUrl: '{{ transaction_url|default("") }}',
    addTransactionUrl: '{{ add_transaction_url|default("") }}',
    rewardUrl: '{{ reward_url|default("") }}',
    addRewardUrl: '{{ add_reward_url|default("") }}',
    ipUrl: '{{ ip_url|default("") }}'
};
</script>
```

---

## 💻 JavaScript промени

### **2. Разширен customer-form.js модул**
**Файл:** `system/storage/theme/Backend/View/Javascript/customer-form.js`

#### **Нови конфигурации:**
```javascript
config: {
    userToken: '',
    customerId: 0,
    countries: [],
    unlockUrl: '',
    historyUrl: '',           // НОВ
    addHistoryUrl: '',        // НОВ
    transactionUrl: '',       // НОВ
    addTransactionUrl: '',    // НОВ
    rewardUrl: '',            // НОВ
    addRewardUrl: '',         // НОВ
    ipUrl: '',                // НОВ
    maxConcurrentRequests: 10,
    activeRequests: 0
}
```

#### **Нови DOM елементи:**
```javascript
elements: {
    // ... съществуващи елементи
    historyList: null,              // НОВ
    historyComment: null,           // НОВ
    addHistoryBtn: null,            // НОВ
    transactionList: null,          // НОВ
    transactionDescription: null,   // НОВ
    transactionAmount: null,        // НОВ
    addTransactionBtn: null,        // НОВ
    rewardList: null,               // НОВ
    rewardDescription: null,        // НОВ
    rewardPoints: null,             // НОВ
    addRewardBtn: null,             // НОВ
    ipList: null                    // НОВ
}
```

#### **Обновен switchTab метод:**
```javascript
customerForm_switchTab(event) {
    // ... existing tab switching logic
    
    // Load data for specific tabs
    if (this.config.customerId > 0) {
        switch (targetTab) {
            case 'tab-history':
                this.customerForm_loadHistory();
                break;
            case 'tab-transaction':
                this.customerForm_loadTransactions();
                break;
            case 'tab-reward':
                this.customerForm_loadRewards();
                break;
            case 'tab-ip':
                this.customerForm_loadIpAddresses();
                break;
        }
    }
}
```

#### **Нови методи:**

##### **1. History операции:**
```javascript
customerForm_loadHistory()     // Зарежда историята на клиента
customerForm_addHistory()      // Добавя нов коментар в историята
```

##### **2. Transaction операции:**
```javascript
customerForm_loadTransactions()  // Зарежда транзакциите на клиента
customerForm_addTransaction()    // Добавя нова транзакция
```

##### **3. Reward операции:**
```javascript
customerForm_loadRewards()     // Зарежда reward points на клиента
customerForm_addReward()       // Добавя нови reward points
```

##### **4. IP операции:**
```javascript
customerForm_loadIpAddresses() // Зарежда IP адресите на клиента
```

---

## 🔄 Функционален поток

### **Tab switching:**
1. **Потребителят натиска таб** → `customerForm_switchTab()` се извиква
2. **UI се обновява** → активен таб се маркира, съдържанието се показва
3. **Данни се зареждат** → ако е нов клиент (customer_id > 0), се зареждат данни за таба
4. **AJAX заявка** → fetch() към съответния URL за данни
5. **Резултат се показва** → HTML се вмъква в съответния контейнер

### **Добавяне на данни:**
1. **Потребителят попълва форма** → въвежда данни в полетата
2. **Натиска бутон** → `customerForm_addHistory/Transaction/Reward()` се извиква
3. **Валидация** → проверява се дали полетата са попълнени правилно
4. **AJAX заявка** → POST заявка към backend с FormData
5. **Response обработка** → показва се success/error notification
6. **Refresh данни** → автоматично се презареждат данните за таба

### **Error handling:**
- ✅ **Network errors** → catch блокове с fallback съобщения
- ✅ **Validation errors** → проверка на полета преди изпращане
- ✅ **Backend errors** → JSON response с error съобщения
- ✅ **Loading states** → показване на loading индикатори

---

## 🎨 Дизайн особености

### **Tailwind CSS стилове:**
- ✅ **Консистентни цветове** → primary, gray-50, gray-900
- ✅ **Responsive grid** → grid-cols-1 md:grid-cols-2
- ✅ **Spacing система** → space-y-6, gap-4, p-6
- ✅ **Border radius** → rounded-lg, rounded-md
- ✅ **Hover effects** → hover:bg-primary/90, transition-colors

### **RemixIcon икони:**
- ✅ **Add операции** → `ri-add-line`
- ✅ **Консистентност** → същите икони в цялата тема

### **Responsive дизайн:**
- ✅ **Mobile first** → стилове започват от mobile
- ✅ **Tablet/Desktop** → md: breakpoints за по-големи екрани
- ✅ **Tab overflow** → overflow-x-auto за много табове

---

## 🧪 Тестване

### **За тестване на функционалността:**

#### **1. Tab switching:**
1. Отвори редактиране на съществуващ клиент
2. Натисни всеки таб и провери дали се показва правилното съдържание
3. Провери дали данните се зареждат автоматично

#### **2. History операции:**
1. Отвори History таба
2. Добави коментар и провери дали се записва
3. Провери дали се показва в списъка

#### **3. Transaction операции:**
1. Отвори Transaction таба
2. Добави транзакция с положителна/отрицателна сума
3. Провери дали балансът се обновява

#### **4. Reward операции:**
1. Отвори Reward таба
2. Добави reward points
3. Провери дали общите точки се обновяват

#### **5. IP операции:**
1. Отвори IP таба
2. Провери дали се показват IP адресите на клиента

### **Browser console проверки:**
```javascript
// Проверка на конфигурацията:
console.log(CustomerFormModule.config);

// Проверка на DOM елементи:
console.log(CustomerFormModule.elements);

// Тест на tab switching:
CustomerFormModule.customerForm_switchTab({
    preventDefault: () => {},
    target: { dataset: { tab: 'tab-history' } }
});
```

---

**Статус:** ✅ Завършено и готово за тестване  
**Дата:** 2025-07-19  
**Файлове:** 
- `system/storage/theme/Backend/View/Template/customer/customer_form.twig` (ОБНОВЕН)
- `system/storage/theme/Backend/View/Javascript/customer-form.js` (РАЗШИРЕН)
