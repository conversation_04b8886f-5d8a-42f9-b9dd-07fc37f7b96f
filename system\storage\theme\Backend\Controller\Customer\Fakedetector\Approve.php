<?php

namespace Theme25\Backend\Controller\Customer\Fakedetector;

class Approve extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('fake_detector_approve.log');
    }

    /**
     * Одобрява маркиран потребител
     * (адаптирано от оригиналния fake_detector.php approve метод)
     */
    public function execute() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            
            if (!$customer_id) {
                $customer_id = (int)$this->requestPost('customer_id', 0);
            }
            
            if ($customer_id) {
                try {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                    
                    // Проверка дали потребителят е маркиран
                    $flagged_customer = $this->fakeDetectorModel->getFlaggedCustomer($customer_id);
                    
                    if (!$flagged_customer) {
                        $json['error'] = 'Потребителят не е намерен в списъка с маркирани!';
                    } elseif ($flagged_customer['status'] === 'approved') {
                        $json['warning'] = 'Потребителят вече е одобрен!';
                    } elseif ($flagged_customer['status'] === 'deleted') {
                        $json['error'] = 'Потребителят е изтрит и не може да бъде одобрен!';
                    } else {
                        // Одобряване на потребителя
                        $result = $this->fakeDetectorModel->approveCustomer($customer_id);
                        
                        if ($result) {
                            // Логване на действието
                            $this->logApprovalAction($customer_id, $flagged_customer);
                            
                            $json['success'] = 'Потребителят е одобрен успешно!';
                            
                            // Ако заявката е AJAX, не пренасочваме
                            if (!$this->isAjaxRequest()) {
                                $json['redirect'] = $this->getAdminLink('customer/fakedetector');
                            }
                        } else {
                            $json['error'] = 'Грешка при одобряване на потребителя!';
                        }
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при одобряване: ' . $e->getMessage();
                }
            } else {
                $json['error'] = 'Невалиден ID на потребител!';
            }
        }

        // Ако не е AJAX заявка, пренасочваме към основната страница
        if (!$this->isAjaxRequest() && isset($json['success'])) {
            $this->session->data['success'] = $json['success'];
            $this->response->redirect($this->getAdminLink('customer/fakedetector'));
        } else {
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Масово одобряване на потребители
     */
    public function bulk() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_ids = $this->requestPost('customer_ids', []);
            
            if (empty($customer_ids) || !is_array($customer_ids)) {
                $json['error'] = 'Не са избрани потребители за одобряване!';
            } else {
                try {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                    
                    $approved_count = 0;
                    $errors = [];
                    
                    foreach ($customer_ids as $customer_id) {
                        $customer_id = (int)$customer_id;
                        
                        if ($customer_id) {
                            $flagged_customer = $this->fakeDetectorModel->getFlaggedCustomer($customer_id);
                            
                            if ($flagged_customer && $flagged_customer['status'] === 'pending') {
                                if ($this->fakeDetectorModel->approveCustomer($customer_id)) {
                                    $approved_count++;
                                    $this->logApprovalAction($customer_id, $flagged_customer);
                                } else {
                                    $errors[] = "Грешка при одобряване на потребител ID: $customer_id";
                                }
                            }
                        }
                    }
                    
                    if ($approved_count > 0) {
                        $json['success'] = sprintf('Одобрени са %d потребители!', $approved_count);
                        
                        if (!empty($errors)) {
                            $json['warnings'] = $errors;
                        }
                    } else {
                        $json['error'] = 'Нито един потребител не е одобрен!';
                        if (!empty($errors)) {
                            $json['details'] = $errors;
                        }
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при масово одобряване: ' . $e->getMessage();
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Одобряване с бележка
     */
    public function withNote() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_id = (int)$this->requestPost('customer_id', 0);
            $note = trim($this->requestPost('note', ''));
            
            if (!$customer_id) {
                $json['error'] = 'Невалиден ID на потребител!';
            } else {
                try {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                    
                    $flagged_customer = $this->fakeDetectorModel->getFlaggedCustomer($customer_id);
                    
                    if (!$flagged_customer) {
                        $json['error'] = 'Потребителят не е намерен в списъка с маркирани!';
                    } elseif ($flagged_customer['status'] !== 'pending') {
                        $json['error'] = 'Потребителят не е в статус "чакащ" и не може да бъде одобрен!';
                    } else {
                        // Одобряване с бележка
                        $result = $this->fakeDetectorModel->approveCustomerWithNote($customer_id, $note);
                        
                        if ($result) {
                            $this->logApprovalAction($customer_id, $flagged_customer, $note);
                            $json['success'] = 'Потребителят е одобрен успешно с бележка!';
                        } else {
                            $json['error'] = 'Грешка при одобряване на потребителя!';
                        }
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при одобряване с бележка: ' . $e->getMessage();
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Отмяна на одобрение (връщане в статус pending)
     */
    public function revoke() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_id = (int)$this->requestPost('customer_id', 0);
            $reason = trim($this->requestPost('reason', ''));
            
            if (!$customer_id) {
                $json['error'] = 'Невалиден ID на потребител!';
            } else {
                try {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                    
                    $flagged_customer = $this->fakeDetectorModel->getFlaggedCustomer($customer_id);
                    
                    if (!$flagged_customer) {
                        $json['error'] = 'Потребителят не е намерен в списъка с маркирани!';
                    } elseif ($flagged_customer['status'] !== 'approved') {
                        $json['error'] = 'Потребителят не е одобрен и не може да бъде отменен!';
                    } else {
                        // Отмяна на одобрението
                        $result = $this->fakeDetectorModel->revokeApproval($customer_id, $reason);
                        
                        if ($result) {
                            $this->logRevocationAction($customer_id, $flagged_customer, $reason);
                            $json['success'] = 'Одобрението е отменено успешно!';
                        } else {
                            $json['error'] = 'Грешка при отмяна на одобрението!';
                        }
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при отмяна на одобрение: ' . $e->getMessage();
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Получава информация за одобрен потребител
     */
    public function info() {
        $json = [];

        if (!$this->hasPermission('access', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за достъп!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            
            if ($customer_id) {
                try {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                    
                    $info = $this->fakeDetectorModel->getApprovalInfo($customer_id);
                    
                    if ($info) {
                        $json['info'] = $info;
                    } else {
                        $json['error'] = 'Информацията за одобрението не е намерена!';
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при получаване на информация: ' . $e->getMessage();
                }
            } else {
                $json['error'] = 'Невалиден ID на потребител!';
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Логва действието за одобряване
     */
    private function logApprovalAction($customer_id, $flagged_customer, $note = '') {
        $log_data = [
            'action' => 'approve',
            'customer_id' => $customer_id,
            'email' => $flagged_customer['email'],
            'ip' => $flagged_customer['ip'],
            'original_reason' => $flagged_customer['reason'],
            'admin_user' => $this->user->getUserName(),
            'admin_id' => $this->user->getId(),
            'note' => $note,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->writeLog('Одобрен потребител: ' . json_encode($log_data, JSON_UNESCAPED_UNICODE));
    }

    /**
     * Логва действието за отмяна на одобрение
     */
    private function logRevocationAction($customer_id, $flagged_customer, $reason = '') {
        $log_data = [
            'action' => 'revoke_approval',
            'customer_id' => $customer_id,
            'email' => $flagged_customer['email'],
            'ip' => $flagged_customer['ip'],
            'revocation_reason' => $reason,
            'admin_user' => $this->user->getUserName(),
            'admin_id' => $this->user->getId(),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->writeLog('Отменено одобрение: ' . json_encode($log_data, JSON_UNESCAPED_UNICODE));
    }

    /**
     * Проверява дали заявката е AJAX
     */
    private function isAjaxRequest() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
