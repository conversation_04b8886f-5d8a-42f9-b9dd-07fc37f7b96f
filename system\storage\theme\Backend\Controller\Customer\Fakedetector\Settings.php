<?php

namespace Theme25\Backend\Controller\Customer\Fakedetector;

/**
 * Fake Detector Settings Sub-Controller
 * Управление на настройките на модула
 */
class Settings extends \Theme25\ControllerSubMethods {

    private $helper;

    public function __construct($controller) {
        parent::__construct($controller);
        $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
        $this->helper = new \Theme25\Backend\Helper\Fakedetectorhelper($this->db, $this->config);
        $this->loadScripts();
    }

    public function index() {
        return $this->execute();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'fake-detector-settings.js',
        ]);
    }

    /**
     * Основен метод за показване на настройките
     */
    public function execute() {

        // Обработка на POST заявки
        if ($this->isPost()) {
            $this->handleSettingsUpdate();
            return;
        }

        $this->setTitle('Настройки на Fake Detector');
        $this->initAdminData();
        
        $this->prepareSettingsData()
             ->prepareFormData();

        $this->renderTemplateWithDataAndOutput('customer/fake_detector_settings');
    }

    /**
     * Обработва обновяването на настройките
     */
    private function handleSettingsUpdate() {
        try {
            $settings = $this->requestPost('settings', []);

            // Валидация на настройките
            $validated_settings = $this->validateSettings($settings);

            // Запазване в базата данни
            foreach ($validated_settings as $key => $value) {
                $this->fakeDetectorModel->saveSetting($key, $value);
            }
            
            $this->setSuccessMessage('Настройките са запазени успешно!');
            
        } catch (Exception $e) {
            $this->setErrorMessage('Грешка при запазване на настройките: ' . $e->getMessage());
        }
        
        // Пренасочване за избягване на повторно изпращане
        $this->redirect($this->getAdminLink('customer/fakedetector/settings'));
    }

    /**
     * Валидира настройките
     */
    private function validateSettings($settings) {
        $validated = [];
        
        // Валидация на числови стойности
        $validated['min_username_length'] = max(1, min(50, (int)($settings['min_username_length'] ?? 10)));
        $validated['max_accounts_per_ip'] = max(1, min(100, (int)($settings['max_accounts_per_ip'] ?? 3)));
        $validated['scan_interval_hours'] = max(1, min(168, (int)($settings['scan_interval_hours'] ?? 24)));
        $validated['delete_after_days'] = max(0, min(365, (int)($settings['delete_after_days'] ?? 30)));
        
        // Валидация на regex
        $regex = $settings['regex_username'] ?? '/^[a-zA-Z0-9]{10,}$/';
        if (@preg_match($regex, 'test') !== false) {
            $validated['regex_username'] = $regex;
        } else {
            $validated['regex_username'] = '/^[a-zA-Z0-9]{10,}$/';
        }
        
        // Булеви стойности
        $validated['only_no_orders'] = !empty($settings['only_no_orders']);
        $validated['check_blacklist'] = !empty($settings['check_blacklist']);
        $validated['auto_scan_enabled'] = !empty($settings['auto_scan_enabled']);
        $validated['test_mode'] = !empty($settings['test_mode']);
        $validated['enable_logging'] = !empty($settings['enable_logging']);
        
        // Обработка на съмнителните домейни
        $bad_domains_text = $settings['bad_domains_text'] ?? '';
        $bad_domains = [];
        
        if (!empty($bad_domains_text)) {
            $lines = explode("\n", $bad_domains_text);
            foreach ($lines as $line) {
                $domain = trim($line);
                if (!empty($domain) && filter_var('test@' . $domain, FILTER_VALIDATE_EMAIL)) {
                    $bad_domains[] = strtolower($domain);
                }
            }
        }
        
        $validated['bad_domains'] = array_unique($bad_domains);
        
        return $validated;
    }

    /**
     * Подготвя данните за настройките
     */
    private function prepareSettingsData() {
        // Зареждане на текущите настройки
        $current_settings = $this->fakeDetectorModel->getSettings();

        $all_settings = $this->helper->getAllSettings();
        
        $this->setData([
            'current_settings' => $current_settings,
            'all_settings' => $all_settings
        ]);
        
        return $this;
    }

    /**
     * Подготвя данните за формата
     */
    private function prepareFormData() {
        $this->setData([
            'heading_title' => 'Настройки на Fake Detector',
            'action_url' => $this->getAdminLink('customer/fakedetector/settings'),
            'back_url' => $this->getAdminLink('customer/fakedetector'),
            'save_url' => $this->getAdminLink('customer/fakedetector/settings/save'),
            'test_url' => $this->getAdminLink('customer/fakedetector/settings/test'),
            'reset_url' => $this->getAdminLink('customer/fakedetector/settings/reset'),
            'export_url' => $this->getAdminLink('customer/fakedetector/settings/export'),
            'import_url' => $this->getAdminLink('customer/fakedetector/settings/import'),
            'export_url' => $this->getAdminLink('customer/fakedetector/settings/export'),
            'import_url' => $this->getAdminLink('customer/fakedetector/settings/import')
        ]);
        
        return $this;
    }

    /**
     * Запазване на настройките
     */
    public function save() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            try {
                // Получаване на настройките от POST
                $enabled = (bool)$this->requestPost('enabled', false);
                $auto_scan = (bool)$this->requestPost('auto_scan', false);
                $scan_interval = (int)$this->requestPost('scan_interval', 60);
                $max_customers = (int)$this->requestPost('max_customers', 1000);
                $email_enabled = (bool)$this->requestPost('email_enabled', false);
                $email_address = trim($this->requestPost('email_address', ''));
                $email_subject = trim($this->requestPost('email_subject', ''));
                $log_enabled = (bool)$this->requestPost('log_enabled', false);
                $log_level = $this->requestPost('log_level', 'info');
                $log_retention = (int)$this->requestPost('log_retention', 30);
                $blacklist_enabled = (bool)$this->requestPost('blacklist_enabled', false);
                $auto_blacklist = (bool)$this->requestPost('auto_blacklist', false);
                $detection_rules = $this->requestPost('detection_rules', []);

                // Валидация
                $validation_errors = [];

                if ($email_enabled && !filter_var($email_address, FILTER_VALIDATE_EMAIL)) {
                    $validation_errors[] = 'Невалиден email адрес';
                }

                if ($scan_interval < 1 || $scan_interval > 1440) {
                    $validation_errors[] = 'Интервалът за сканиране трябва да е между 1 и 1440 минути';
                }

                if ($max_customers < 1 || $max_customers > 10000) {
                    $validation_errors[] = 'Максималният брой потребители трябва да е между 1 и 10000';
                }

                if ($log_retention < 1 || $log_retention > 365) {
                    $validation_errors[] = 'Периодът за съхранение на логове трябва да е между 1 и 365 дни';
                }

                if ($enabled && empty($detection_rules)) {
                    $validation_errors[] = 'Моля изберете поне едно правило за детекция';
                }

                if (!empty($validation_errors)) {
                    $json['error'] = implode('; ', $validation_errors);
                } else {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');

                    // Запазване на настройките
                    $settings = [
                        'enabled' => $enabled,
                        'auto_scan' => $auto_scan,
                        'scan_interval' => $scan_interval,
                        'max_customers' => $max_customers,
                        'email_enabled' => $email_enabled,
                        'email_address' => $email_address,
                        'email_subject' => $email_subject,
                        'log_enabled' => $log_enabled,
                        'log_level' => $log_level,
                        'log_retention' => $log_retention,
                        'blacklist_enabled' => $blacklist_enabled,
                        'auto_blacklist' => $auto_blacklist,
                        'detection_rules' => json_encode($detection_rules)
                    ];

                    foreach ($settings as $key => $value) {
                        $this->fakeDetectorModel->saveSetting($key, $value);
                    }

                    $json['success'] = true;
                    $json['message'] = 'Настройките са запазени успешно!';
                }

            } catch (Exception $e) {
                $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Тестване на настройките
     */
    public function test() {

        try {
            // Получаване на настройките от POST
            $settings = $this->requestPost('settings', []);
            $validated_settings = $this->validateSettings($settings);
            
            // Тестване на regex
            $test_names = ['testuser123', 'user', 'abcdefghijk', 'test@user', 'normalname'];
            $regex_results = [];
            
            foreach ($test_names as $name) {
                $matches = preg_match($validated_settings['regex_username'], $name);
                $regex_results[] = [
                    'name' => $name,
                    'matches' => $matches,
                    'result' => $matches ? 'Подозрително' : 'Нормално'
                ];
            }
            
            // Тестване на домейни
            $test_domains = ['gmail.com', 'hotmail.com', 'tempmail.org', 'example.com'];
            $domain_results = [];
            
            foreach ($test_domains as $domain) {
                $is_bad = in_array(strtolower($domain), $validated_settings['bad_domains']);
                $domain_results[] = [
                    'domain' => $domain,
                    'is_bad' => $is_bad,
                    'result' => $is_bad ? 'Блокиран' : 'Разрешен'
                ];
            }
            
            $this->jsonResponse([
                'success' => true,
                'regex_test' => $regex_results,
                'domain_test' => $domain_results,
                'validated_settings' => $validated_settings
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Възстановяване на настройки по подразбиране
     */
    public function reset() {
        try {
 
            // Изтриване на всички текущи настройки
            $this->db->query("DELETE FROM " . DB_PREFIX . "fake_detector_settings");
            
            $this->setSuccessMessage('Настройките са възстановени по подразбиране!');
            
        } catch (Exception $e) {
            $this->setErrorMessage('Грешка при възстановяване: ' . $e->getMessage());
        }
        
        $this->redirect($this->getAdminLink('customer/fakedetector/settings'));
    }

    /**
     * Експорт на настройките
     */
    public function export() {
        try {

            $settings_json = $this->helper->exportSettings();
            
            $filename = 'fake_detector_settings_' . date('Y-m-d_H-i-s') . '.json';
            
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . strlen($settings_json));
            
            echo $settings_json;
            exit;
            
        } catch (Exception $e) {
            $this->setErrorMessage('Грешка при експорт: ' . $e->getMessage());
            $this->redirect($this->getAdminLink('customer/fakedetector/settings'));
        }
    }

    /**
     * Импорт на настройки
     */
    public function import() {
        try {
            $file = $_FILES['settings_file'] ?? null;
            
            if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('Грешка при качване на файла');
            }
            
            $json_data = file_get_contents($file['tmp_name']);

            $imported_count = $this->helper->importSettings($json_data);
            
            $this->setSuccessMessage("Импортирани са {$imported_count} настройки успешно!");
            
        } catch (Exception $e) {
            $this->setErrorMessage('Грешка при импорт: ' . $e->getMessage());
        }
        
        $this->redirect($this->getAdminLink('customer/fakedetector/settings'));
    }


}
