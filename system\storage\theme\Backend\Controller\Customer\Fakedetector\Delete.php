<?php

namespace Theme25\Backend\Controller\Customer\Fakedetector;

class Delete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('fake_detector_delete.log');
    }

    /**
     * Изтрива маркиран потребител
     * (адаптирано от оригиналния fake_detector.php delete метод)
     */
    public function execute() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            
            if (!$customer_id) {
                $customer_id = (int)$this->requestPost('customer_id', 0);
            }
            
            if ($customer_id) {
                try {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                    
                    // Проверка дали потребителят е маркиран
                    $flagged_customer = $this->fakeDetectorModel->getFlaggedCustomer($customer_id);
                    
                    if (!$flagged_customer) {
                        $json['error'] = 'Потребителят не е намерен в списъка с маркирани!';
                    } elseif ($flagged_customer['status'] === 'deleted') {
                        $json['warning'] = 'Потребителят вече е изтрит!';
                    } else {
                        // Получаване на информация за потребителя преди изтриване
                        $customer_info = $this->getCustomerInfo($customer_id);
                        
                        // Изтриване на потребителя
                        $result = $this->fakeDetectorModel->deleteCustomer($customer_id);
                        
                        if ($result) {
                            // Логване на действието
                            $this->logDeletionAction($customer_id, $flagged_customer, $customer_info);
                            
                            $json['success'] = true;
                            $json['message'] = 'Потребителят е изтрит успешно!';
                            
                            // Ако заявката не е AJAX, пренасочваме
                            if (!$this->isAjaxRequest()) {
                                $json['redirect'] = $this->getAdminLink('customer/fakedetector');
                            }
                        } else {
                            $json['error'] = 'Грешка при изтриване на потребителя!';
                        }
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при изтриване: ' . $e->getMessage();
                }
            } else {
                $json['error'] = 'Невалиден ID на потребител!';
            }
        }

        // Ако не е AJAX заявка, пренасочваме към основната страница
        if (!$this->isAjaxRequest() && isset($json['success'])) {
            $this->session->data['success'] = $json['success'];
            $this->response->redirect($this->getAdminLink('customer/fakedetector'));
        } else {
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Масово изтриване на потребители
     */
    public function bulk() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_ids = $this->requestPost('customer_ids', []);
            
            if (empty($customer_ids) || !is_array($customer_ids)) {
                $json['error'] = 'Не са избрани потребители за изтриване!';
            } else {
                try {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                    
                    $deleted_count = 0;
                    $errors = [];
                    
                    foreach ($customer_ids as $customer_id) {
                        $customer_id = (int)$customer_id;
                        
                        if ($customer_id) {
                            $flagged_customer = $this->fakeDetectorModel->getFlaggedCustomer($customer_id);
                            
                            if ($flagged_customer && $flagged_customer['status'] !== 'deleted') {
                                $customer_info = $this->getCustomerInfo($customer_id);
                                
                                if ($this->fakeDetectorModel->deleteCustomer($customer_id)) {
                                    $deleted_count++;
                                    $this->logDeletionAction($customer_id, $flagged_customer, $customer_info);
                                } else {
                                    $errors[] = "Грешка при изтриване на потребител ID: $customer_id";
                                }
                            }
                        }
                    }
                    
                    if ($deleted_count > 0) {
                        $json['success'] = true;
                        $json['message'] = sprintf('Изтрити са %d потребители!', $deleted_count);
                        
                        if (!empty($errors)) {
                            $json['warnings'] = $errors;
                        }
                    } else {
                        $json['error'] = 'Нито един потребител не е изтрит!';
                        if (!empty($errors)) {
                            $json['details'] = $errors;
                        }
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при масово изтриване: ' . $e->getMessage();
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Изтриване с потвърждение и причина
     */
    public function withConfirmation() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_id = (int)$this->requestPost('customer_id', 0);
            $reason = trim($this->requestPost('reason', ''));
            $confirmed = $this->requestPost('confirmed', false);
            
            if (!$customer_id) {
                $json['error'] = 'Невалиден ID на потребител!';
            } elseif (!$confirmed) {
                $json['error'] = 'Изтриването не е потвърдено!';
            } else {
                try {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                    
                    $flagged_customer = $this->fakeDetectorModel->getFlaggedCustomer($customer_id);
                    
                    if (!$flagged_customer) {
                        $json['error'] = 'Потребителят не е намерен в списъка с маркирани!';
                    } elseif ($flagged_customer['status'] === 'deleted') {
                        $json['error'] = 'Потребителят вече е изтрит!';
                    } else {
                        $customer_info = $this->getCustomerInfo($customer_id);
                        
                        // Изтриване с причина
                        $result = $this->fakeDetectorModel->deleteCustomerWithReason($customer_id, $reason);
                        
                        if ($result) {
                            $this->logDeletionAction($customer_id, $flagged_customer, $customer_info, $reason);
                            $json['success'] = true;
                            $json['message'] = 'Потребителят е изтрит успешно с причина!';
                        } else {
                            $json['error'] = 'Грешка при изтриване на потребителя!';
                        }
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при изтриване с потвърждение: ' . $e->getMessage();
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Възстановяване на изтрит потребител (ако е възможно)
     */
    public function restore() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_id = (int)$this->requestPost('customer_id', 0);
            
            if (!$customer_id) {
                $json['error'] = 'Невалиден ID на потребител!';
            } else {
                try {
                    $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                    
                    $flagged_customer = $this->fakeDetectorModel->getFlaggedCustomer($customer_id);
                    
                    if (!$flagged_customer) {
                        $json['error'] = 'Потребителят не е намерен в списъка с маркирани!';
                    } elseif ($flagged_customer['status'] !== 'deleted') {
                        $json['error'] = 'Потребителят не е изтрит и не може да бъде възстановен!';
                    } else {
                        // Опит за възстановяване
                        $result = $this->fakeDetectorModel->restoreCustomer($customer_id);
                        
                        if ($result['success']) {
                            $this->logRestorationAction($customer_id, $flagged_customer);
                            $json['success'] = true;
                            $json['message'] = 'Потребителят е възстановен успешно!';
                        } else {
                            $json['error'] = $result['error'] ?: 'Грешка при възстановяване на потребителя!';
                        }
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при възстановяване: ' . $e->getMessage();
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Получава информация за потребителя преди изтриване
     */
    private function getCustomerInfo($customer_id) {
        try {
            $this->loadModelAs('customer/customer', 'customerModel');
            return $this->customerModel->getCustomer($customer_id);
        } catch (Exception $e) {
            $this->writeLog('Грешка при получаване на информация за потребител: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Логва действието за изтриване
     */
    private function logDeletionAction($customer_id, $flagged_customer, $customer_info = null, $reason = '') {
        $log_data = [
            'action' => 'delete',
            'customer_id' => $customer_id,
            'email' => $flagged_customer['email'],
            'ip' => $flagged_customer['ip'],
            'original_reason' => $flagged_customer['reason'],
            'deletion_reason' => $reason,
            'admin_user' => $this->user->getUserName(),
            'admin_id' => $this->user->getId(),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        if ($customer_info) {
            $log_data['customer_data'] = [
                'firstname' => $customer_info['firstname'],
                'lastname' => $customer_info['lastname'],
                'telephone' => $customer_info['telephone'],
                'date_added' => $customer_info['date_added']
            ];
        }

        $this->writeLog('Изтрит потребител: ' . json_encode($log_data, JSON_UNESCAPED_UNICODE));
    }

    /**
     * Логва действието за възстановяване
     */
    private function logRestorationAction($customer_id, $flagged_customer) {
        $log_data = [
            'action' => 'restore',
            'customer_id' => $customer_id,
            'email' => $flagged_customer['email'],
            'ip' => $flagged_customer['ip'],
            'admin_user' => $this->user->getUserName(),
            'admin_id' => $this->user->getId(),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->writeLog('Възстановен потребител: ' . json_encode($log_data, JSON_UNESCAPED_UNICODE));
    }

    /**
     * Проверява дали заявката е AJAX
     */
    private function isAjaxRequest() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Получава статистики за изтриванията
     */
    public function getStats() {
        $json = [];

        if (!$this->hasPermission('access', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за достъп!';
        } else {
            try {
                $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                
                $stats = $this->fakeDetectorModel->getDeletionStatistics();
                $json['stats'] = $stats;
                
            } catch (Exception $e) {
                $json['error'] = 'Грешка при получаване на статистики: ' . $e->getMessage();
            }
        }

        $this->setJSONResponseOutput($json);
    }
}
