/**
 * Fake Detector Manual Scan Module JavaScript
 * Следва BackendModule pattern на Rakla.bg проекта
 */

(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initFakeDetectorScan();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        Object.assign(BackendModule, {

            // Конфигурация за Manual Scan
            fakeDetectorScan: {
                config: null,
                elements: {},
                isInitialized: false,
                progress: {
                    current: 0,
                    total: 0,
                    interval: null
                }
            },

            // Инициализация на Manual Scan модула
            initFakeDetectorScan: function() {
                if (this.fakeDetectorScan.isInitialized) return;

                // Проверка дали сме на Manual Scan страница
                if (!window.manualScanConfig) return;

                this.fakeDetectorScan.config = window.manualScanConfig;
                this.cacheScanElements();
                this.bindScanEvents();
                this.initScanComponents();

                this.fakeDetectorScan.isInitialized = true;
                this.log('Fake Detector Manual Scan module initialized');
            },

            // Кеширане на DOM елементи
            cacheScanElements: function() {
                this.fakeDetectorScan.elements = {
                    // Основни форми
                    scanForm: document.getElementById('form-manual-scan'),

                    // Бутони за действия
                    runButton: document.getElementById('button-run-scan'),
                    runButtonBottom: document.getElementById('button-run-scan-bottom'),
                    resetButton: document.getElementById('button-reset'),

                    // Резултати и прогрес
                    testResults: document.getElementById('test-results'),
                    testResultsContent: document.getElementById('test-results-content'),
                    scanProgress: document.getElementById('scan-progress'),

                    // Модален прозорец за потвърждение
                    confirmModal: document.getElementById('confirm-modal'),
                    confirmModalTitle: document.getElementById('confirm-modal-title'),
                    confirmModalMessage: document.getElementById('confirm-modal-message'),
                    confirmModalConfirm: document.getElementById('confirm-modal-confirm'),
                    confirmModalCancel: document.getElementById('confirm-modal-cancel'),
                    progressBar: document.getElementById('progress-bar'),
                    progressText: document.getElementById('progress-text'),
                    progressTextVisible: document.getElementById('progress-text-visible'),
                    scanStatus: document.getElementById('scan-status'),

                    // Входни полета
                    customerIdsTextarea: document.getElementById('input-customer-ids'),
                    dateFromInput: document.getElementById('input-date-from'),
                    dateToInput: document.getElementById('input-date-to'),
                    testModeCheckbox: document.querySelector('input[name="test_mode"]'),

                    // Правила
                    rulesCheckboxes: document.querySelectorAll('input[name="rules[]"]'),

                    // Модали
                    loadingOverlay: document.getElementById('loading')
                };
            },

            // Свързване на събития
            bindScanEvents: function() {
                const self = this;

                // Сканиране (header бутон)
                if (self.fakeDetectorScan.elements.runButton) {
                    self.fakeDetectorScan.elements.runButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.runScan();
                    });
                }

                // Сканиране (bottom бутон)
                if (self.fakeDetectorScan.elements.runButtonBottom) {
                    self.fakeDetectorScan.elements.runButtonBottom.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.runScan();
                    });
                }

                // Reset бутон
                if (self.fakeDetectorScan.elements.resetButton) {
                    self.fakeDetectorScan.elements.resetButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.resetForm();
                    });
                }

                // Модален прозорец за потвърждение
                if (self.fakeDetectorScan.elements.confirmModalCancel) {
                    self.fakeDetectorScan.elements.confirmModalCancel.addEventListener('click', function() {
                        self.hideConfirmModal();
                    });
                }

                // Валидация на customer IDs в реално време
                if (self.fakeDetectorScan.elements.customerIdsTextarea) {
                    self.fakeDetectorScan.elements.customerIdsTextarea.addEventListener('input', function() {
                        self.validateCustomerIds();
                    });
                }

                // Промяна на test mode
                if (self.fakeDetectorScan.elements.testModeCheckbox) {
                    self.fakeDetectorScan.elements.testModeCheckbox.addEventListener('change', function() {
                        self.updateScanButtonText();
                    });
                }

                // Промяна на правилата
                self.fakeDetectorScan.elements.rulesCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        self.validateScanForm();
                    });
                });
            },

            // Инициализация на компоненти
            initScanComponents: function() {
                this.initScanDatePickers();
                this.validateCustomerIds();
                this.updateScanButtonText();
                this.validateScanForm();
            },

            // Тестово сканиране
            runTestScan: function() {
                const self = this;

                if (!self.validateScanForm()) {
                    return;
                }

                const params = self.getScanParams();
                params.test_mode = true;

                self.showScanLoading('Изпълнение на тестово сканиране...');

                fetch(self.fakeDetectorScan.config.testUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(params)
                })
                .then(response => response.json())
                .then(data => {
                    self.hideScanLoading();

                    // Debug logging
                    console.log('Test scan response:', data);

                    if (data.success) {
                        self.showTestResults(data.results);
                    } else {
                        self.showAlert('error', data.error || 'Възникна грешка при тестовото сканиране');
                    }
                })
                .catch(error => {
                    self.hideScanLoading();
                    console.error('Test scan error:', error);
                    self.showAlert('error', 'Възникна грешка при тестовото сканиране');
                });
            },

            // Реално сканиране
            runRealScan: function() {
                const self = this;

                if (!self.validateScanForm()) {
                    return;
                }

                const params = self.getScanParams();

                if (!confirm('Сигурни ли сте, че искате да стартирате реално сканиране? Това може да отнеме време.')) {
                    return;
                }

                self.startScanProgress();

                fetch(self.fakeDetectorScan.config.actionUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(params)
                })
                .then(response => response.json())
                .then(data => {
                    self.stopScanProgress();

                    if (data.success) {
                        self.showAlert('success', data.message || 'Сканирането завърши успешно');

                        // Показване на резултатите
                        if (data.results) {
                            self.showTestResults(data.results);
                        }

                        // Презареждане на страницата след 3 секунди
                        setTimeout(() => {
                            window.location.href = self.fakeDetectorScan.config.backUrl;
                        }, 3000);
                    } else {
                        self.showAlert('error', data.error || 'Възникна грешка при сканирането');
                    }
                })
                .catch(error => {
                    self.stopScanProgress();
                    console.error('Real scan error:', error);
                    self.showAlert('error', 'Възникна грешка при сканирането');
                });
            },

            // Получаване на параметрите за сканиране
            getScanParams: function() {
                const elements = this.fakeDetectorScan.elements;

                const params = {
                    user_token: this.config.userToken,
                    test_mode: elements.testModeCheckbox ? elements.testModeCheckbox.checked : true,
                    date_from: elements.dateFromInput ? elements.dateFromInput.value : '',
                    date_to: elements.dateToInput ? elements.dateToInput.value : '',
                    customer_ids: elements.customerIdsTextarea ? elements.customerIdsTextarea.value.trim() : '',
                    rules: []
                };

                // Събиране на избраните правила
                elements.rulesCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        params.rules.push(checkbox.value);
                    }
                });

                return params;
            },

            // Валидация на формата за сканиране
            validateScanForm: function() {
                const elements = this.fakeDetectorScan.elements;

                // Проверка дали има избрани правила
                const selectedRules = Array.from(elements.rulesCheckboxes).filter(cb => cb.checked);
                if (selectedRules.length === 0) {
                    this.showAlert('warning', 'Моля изберете поне едно правило за сканиране');
                    return false;
                }

                // Валидация на customer IDs ако са въведени
                if (elements.customerIdsTextarea && elements.customerIdsTextarea.value.trim()) {
                    if (!this.validateCustomerIds()) {
                        return false;
                    }
                }

                return true;
            },

            // Валидация на customer IDs
            validateCustomerIds: function() {
                const elements = this.fakeDetectorScan.elements;
                if (!elements.customerIdsTextarea) return true;

                const value = elements.customerIdsTextarea.value.trim();

                if (!value) {
                    elements.customerIdsTextarea.classList.remove('border-red-300', 'border-green-300');
                    return true;
                }

                // Проверка дали всички стойности са числа
                const ids = value.split(',').map(id => id.trim()).filter(id => id);
                const invalidIds = ids.filter(id => !/^\d+$/.test(id));

                if (invalidIds.length > 0) {
                    elements.customerIdsTextarea.classList.remove('border-green-300');
                    elements.customerIdsTextarea.classList.add('border-red-300');
                    this.showAlert('error', 'Невалидни customer ID-та: ' + invalidIds.join(', '));
                    return false;
                } else {
                    elements.customerIdsTextarea.classList.remove('border-red-300');
                    elements.customerIdsTextarea.classList.add('border-green-300');
                    return true;
                }
            },

            // Обновяване на текста на scan бутона
            updateScanButtonText: function() {
                const elements = this.fakeDetectorScan.elements;
                if (!elements.runButton || !elements.testModeCheckbox) return;

                const isTestMode = elements.testModeCheckbox.checked;
                elements.runButton.textContent = isTestMode ? 'Тестово сканиране' : 'Реално сканиране';
            },

            // Инициализация на date pickers
            initScanDatePickers: function() {
                const dateInputs = document.querySelectorAll('#input-date-from, #input-date-to');
                dateInputs.forEach(input => {
                    if (input) {
                        input.type = 'date';
                    }
                });
            },

            // Стартиране на прогрес бар
            startScanProgress: function() {
                const elements = this.fakeDetectorScan.elements;

                if (elements.scanProgress) {
                    elements.scanProgress.style.display = 'block';
                }

                if (elements.progressBar) {
                    elements.progressBar.style.width = '0%';
                }

                if (elements.progressText) {
                    elements.progressText.textContent = 'Стартиране на сканиране...';
                }

                // Симулация на прогрес
                this.fakeDetectorScan.progress.current = 0;
                this.fakeDetectorScan.progress.total = 100;

                this.fakeDetectorScan.progress.interval = setInterval(() => {
                    this.updateScanProgress();
                }, 1000);
            },

            // Спиране на прогрес бар
            stopScanProgress: function() {
                if (this.fakeDetectorScan.progress.interval) {
                    clearInterval(this.fakeDetectorScan.progress.interval);
                    this.fakeDetectorScan.progress.interval = null;
                }

                const elements = this.fakeDetectorScan.elements;

                if (elements.progressBar) {
                    elements.progressBar.style.width = '100%';
                }

                if (elements.progressText) {
                    elements.progressText.textContent = 'Сканирането завърши';
                }

                // Скриване на прогрес бара след 2 секунди
                setTimeout(() => {
                    if (elements.scanProgress) {
                        elements.scanProgress.style.display = 'none';
                    }
                }, 2000);
            },

            // Обновяване на прогрес бара
            updateScanProgress: function() {
                const progress = this.fakeDetectorScan.progress;
                const elements = this.fakeDetectorScan.elements;

                if (progress.current < progress.total) {
                    progress.current += Math.random() * 10;
                    if (progress.current > progress.total) {
                        progress.current = progress.total;
                    }

                    const percentage = Math.round((progress.current / progress.total) * 100);

                    if (elements.progressBar) {
                        elements.progressBar.style.width = percentage + '%';
                    }

                    if (elements.progressText) {
                        elements.progressText.textContent = `Сканиране в прогрес... ${percentage}%`;
                    }
                }
            },

            // Показване на loading overlay
            showScanLoading: function(message = 'Зареждане...') {
                const overlay = this.fakeDetectorScan.elements.loadingOverlay;
                if (overlay) {
                    const messageElement = overlay.querySelector('span');
                    if (messageElement) {
                        messageElement.textContent = message;
                    }
                    overlay.classList.remove('hidden');
                }
            },

            // Скриване на loading overlay
            hideScanLoading: function() {
                const overlay = this.fakeDetectorScan.elements.loadingOverlay;
                if (overlay) {
                    overlay.classList.add('hidden');
                }
            },

            // Показване на тестови резултати
            showTestResults: function(results) {
                const elements = this.fakeDetectorScan.elements;
                if (!elements.testResults || !elements.testResultsContent) return;

                // Защита срещу undefined/null results
                if (!results || typeof results !== 'object') {
                    console.error('Invalid results object:', results);
                    this.showAlert('error', 'Получени са невалидни резултати от сканирането');
                    return;
                }

                let html = '<div class="space-y-4">';

                html += `<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-medium text-blue-800 mb-2">Обобщение на резултатите</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>Обработени потребители: <span class="font-medium">${results.processed_count || 0}</span></div>
                        <div>Маркирани като подозрителни: <span class="font-medium text-orange-600">${results.flagged_count || 0}</span></div>
                        <div>Време за изпълнение: <span class="font-medium">${results.execution_time || 'N/A'}</span></div>
                        <div>Режим: <span class="font-medium text-blue-600">Тестов</span></div>
                    </div>
                </div>`;

                if (results.flagged_customers && results.flagged_customers.length > 0) {
                    html += '<div class="bg-orange-50 border border-orange-200 rounded-lg p-4">';
                    html += '<h4 class="font-medium text-orange-800 mb-3">Подозрителни потребители</h4>';
                    html += '<div class="space-y-2 max-h-64 overflow-y-auto">';

                    results.flagged_customers.forEach(customer => {
                        html += `<div class="bg-white border border-orange-200 rounded p-3">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="font-medium">ID: ${customer.customer_id}</div>
                                    <div class="text-sm text-gray-600">${customer.email}</div>
                                    <div class="text-sm text-gray-500">IP: ${customer.ip}</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-orange-600">${customer.reason}</div>
                                </div>
                            </div>
                        </div>`;
                    });

                    html += '</div></div>';
                }

                html += '</div>';

                elements.testResultsContent.innerHTML = html;
                elements.testResults.style.display = 'block';
            },

            /**
             * Debug логване
             */
            log: function(message, data) {
                if (window.console && window.console.log) {
                    console.log('[FakeDetectorModule] ' + message, data || '');
                }
            },

            // Унифицирано сканиране
            runScan: function() {
                const self = this;

                if (!self.validateScanForm()) {
                    return;
                }

                const params = self.getScanParams();
                const isTestMode = params.test_mode;

                // Показване на custom modal за потвърждение
                const title = isTestMode ? 'Тестово сканиране' : 'Реално сканиране';
                const message = isTestMode
                    ? 'Сигурни ли сте, че искате да стартирате тестово сканиране? Няма да се запазват промени в базата данни.'
                    : 'Сигурни ли сте, че искате да стартирате реално сканиране? Това може да отнеме време и ще запази промените в базата данни.';

                self.showConfirmModal(title, message, function() {
                    self.executeActualScan(params, isTestMode);
                });
            },

            // Изпълнение на действителното сканиране
            executeActualScan: function(params, isTestMode) {
                const self = this;

                const url = isTestMode ? self.fakeDetectorScan.config.testUrl : self.fakeDetectorScan.config.actionUrl;

                if (isTestMode) {
                    self.showScanLoading('Изпълнение на тестово сканиране...');
                } else {
                    self.startScanProgress();
                }

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(params)
                })
                .then(response => response.json())
                .then(data => {
                    if (isTestMode) {
                        self.hideScanLoading();
                    } else {
                        self.stopScanProgress();
                    }

                    if (data.success) {
                        if (isTestMode) {
                            // За тестово сканиране показваме резултатите и скролваме до тях
                            self.showTestResults(data.results);
                            self.scrollToResults();
                        } else {
                            // За реално сканиране показваме съобщение и redirect
                            self.showAlert('success', data.message || 'Сканирането завърши успешно');

                            if (data.results) {
                                self.showTestResults(data.results);
                                self.scrollToResults();
                            }

                            // Презареждане на страницата след 3 секунди
                            setTimeout(() => {
                                window.location.href = self.fakeDetectorScan.config.backUrl;
                            }, 3000);
                        }
                    } else {
                        self.showAlert('error', data.error || 'Възникна грешка при сканирането');
                    }
                })
                .catch(error => {
                    if (isTestMode) {
                        self.hideScanLoading();
                    } else {
                        self.stopScanProgress();
                    }
                    console.error('Scan error:', error);
                    self.showAlert('error', 'Възникна грешка при сканирането');
                });
            },

            // Показване на модален прозорец за потвърждение
            showConfirmModal: function(title, message, callback) {
                const self = this;
                const elements = self.fakeDetectorScan.elements;

                if (!elements.confirmModal || !elements.confirmModalTitle || !elements.confirmModalMessage || !elements.confirmModalConfirm) {
                    // Fallback към стандартен confirm ако модалният прозорец не съществува
                    if (confirm(message)) {
                        callback();
                    }
                    return;
                }

                // Задаване на съдържание
                elements.confirmModalTitle.textContent = title;
                elements.confirmModalMessage.textContent = message;

                // Показване на модалния прозорец
                elements.confirmModal.classList.remove('hidden');

                // Добавяне на event listener за бутона за потвърждение
                const confirmHandler = function() {
                    self.hideConfirmModal();
                    callback();
                    elements.confirmModalConfirm.removeEventListener('click', confirmHandler);
                };

                elements.confirmModalConfirm.addEventListener('click', confirmHandler);
            },

            // Скриване на модалния прозорец за потвърждение
            hideConfirmModal: function() {
                const elements = this.fakeDetectorScan.elements;
                if (elements.confirmModal) {
                    elements.confirmModal.classList.add('hidden');
                }
            },

            // Скролване до резултатите
            scrollToResults: function() {
                const self = this;
                if (self.fakeDetectorScan.elements.testResults) {
                    setTimeout(() => {
                        self.fakeDetectorScan.elements.testResults.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }, 300);
                }
            },

            // Ресет на формата
            resetForm: function() {
                const self = this;
                if (self.fakeDetectorScan.elements.scanForm) {
                    self.fakeDetectorScan.elements.scanForm.reset();
                    self.validateScanForm();
                }
            }

        });
    }

})();