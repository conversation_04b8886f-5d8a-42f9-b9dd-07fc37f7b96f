/**
 * Fake Detector Manual Scan Module JavaScript
 * Специализиран модул за ръчно сканиране
 */

(function() {
    'use strict';

    // Модул за ръчно сканиране
    const ScanModule = Object.assign({}, window.FakeDetectorModule || {}, {
        
        // Специфична конфигурация за сканирането
        scanConfig: {
            selectors: {
                scanForm: '#form-manual-scan',
                testButton: '#button-test-scan',
                runButton: '#button-run-scan',
                testResults: '#test-results',
                testResultsContent: '#test-results-content',
                scanProgress: '#scan-progress',
                progressBar: '#progress-bar',
                progressText: '#progress-text',
                scanStatus: '#scan-status',
                customerIdsTextarea: '#input-customer-ids',
                dateFromInput: '#input-date-from',
                dateToInput: '#input-date-to',
                rulesCheckboxes: 'input[name="rules[]"]',
                testModeCheckbox: 'input[name="test_mode"]'
            },
            progress: {
                current: 0,
                total: 0,
                interval: null
            }
        },

        // Инициализация на scan модула
        initScan: function() {
            this.bindScanEvents();
            this.initScanDatePickers();
            this.validateCustomerIds();
            this.log('Scan module initialized');
        },

        // Свързване на събития за сканирането
        bindScanEvents: function() {
            const self = this;

            // Тестово сканиране
            $(document).on('click', this.scanConfig.selectors.testButton, function(e) {
                e.preventDefault();
                self.runTestScan();
            });

            // Реално сканиране
            $(document).on('click', this.scanConfig.selectors.runButton, function(e) {
                e.preventDefault();
                self.runRealScan();
            });

            // Валидация на customer IDs при въвеждане
            $(document).on('input', this.scanConfig.selectors.customerIdsTextarea, function() {
                self.validateCustomerIds();
            });

            // Промяна в датите - обновяване на статистики
            $(document).on('change', this.scanConfig.selectors.dateFromInput + ',' + this.scanConfig.selectors.dateToInput, function() {
                self.updatePreScanStats();
            });

            // Промяна в правилата - обновяване на статистики
            $(document).on('change', this.scanConfig.selectors.rulesCheckboxes, function() {
                self.updatePreScanStats();
            });

            // Превключване на тестов режим
            $(document).on('change', this.scanConfig.selectors.testModeCheckbox, function() {
                self.updateScanButtonText();
            });
        },

        // Инициализация на date pickers за сканирането
        initScanDatePickers: function() {
            $(this.scanConfig.selectors.dateFromInput + ',' + this.scanConfig.selectors.dateToInput).datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true,
                language: 'bg'
            });
        },

        // Валидация на customer IDs
        validateCustomerIds: function() {
            const textarea = $(this.scanConfig.selectors.customerIdsTextarea);
            const value = textarea.val().trim();
            
            if (!value) {
                textarea.removeClass('is-invalid is-valid');
                return true;
            }

            // Проверка за валидни числа разделени със запетая
            const ids = value.split(',').map(id => id.trim()).filter(id => id);
            const invalidIds = ids.filter(id => !/^\d+$/.test(id));

            if (invalidIds.length > 0) {
                textarea.removeClass('is-valid').addClass('is-invalid');
                this.showAlert(`Невалидни ID-та: ${invalidIds.join(', ')}`, 'warning');
                return false;
            } else {
                textarea.removeClass('is-invalid').addClass('is-valid');
                return true;
            }
        },

        // Получаване на параметрите за сканиране
        getScanParams: function() {
            const customerIds = $(this.scanConfig.selectors.customerIdsTextarea).val().trim();
            const selectedRules = [];
            
            $(this.scanConfig.selectors.rulesCheckboxes + ':checked').each(function() {
                selectedRules.push($(this).val());
            });

            return {
                test_mode: $(this.scanConfig.selectors.testModeCheckbox).is(':checked') ? 1 : 0,
                date_from: $(this.scanConfig.selectors.dateFromInput).val(),
                date_to: $(this.scanConfig.selectors.dateToInput).val(),
                customer_ids: customerIds ? customerIds.split(',').map(id => id.trim()).filter(id => id) : [],
                rules: selectedRules
            };
        },

        // Тестово сканиране
        runTestScan: function() {
            if (!this.validateScanParams()) {
                return;
            }

            const params = this.getScanParams();
            params.test_mode = 1; // Винаги тестов режим

            this.showProgress('Изпълнение на тестово сканиране...');

            this.sendAjaxRequest('customer/fakedetector/scan/test', params, (response) => {
                this.hideProgress();
                
                if (response.success) {
                    this.showTestResults(response.preview, response.stats);
                }
            });
        },

        // Реално сканиране
        runRealScan: function() {
            if (!this.validateScanParams()) {
                return;
            }

            const params = this.getScanParams();
            const isTestMode = params.test_mode;

            const confirmMessage = isTestMode 
                ? 'Сигурни ли сте, че искате да стартирате тестово сканиране?'
                : 'ВНИМАНИЕ! Сигурни ли сте, че искате да стартирате реално сканиране? Това ще маркира потребители в базата данни!';

            if (!confirm(confirmMessage)) {
                return;
            }

            this.showProgress('Изпълнение на сканиране...');
            this.startProgressSimulation();

            this.sendAjaxRequest('customer/fakedetector/scan/manual', params, (response) => {
                this.hideProgress();
                this.stopProgressSimulation();
                
                if (response.success) {
                    this.showAlert(response.success, 'success');
                    if (response.details) {
                        this.showScanResults(response.details);
                    }
                    
                    // Пренасочване към основната страница след реално сканиране
                    if (!isTestMode) {
                        setTimeout(() => {
                            window.location.href = 'customer/fakedetector';
                        }, 3000);
                    }
                }
            });
        },

        // Валидация на параметрите за сканиране
        validateScanParams: function() {
            const dateFrom = $(this.scanConfig.selectors.dateFromInput).val();
            const dateTo = $(this.scanConfig.selectors.dateToInput).val();

            // Проверка на датите
            if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
                this.showAlert('Началната дата не може да бъде по-късна от крайната!', 'warning');
                return false;
            }

            // Проверка на customer IDs
            if (!this.validateCustomerIds()) {
                return false;
            }

            // Проверка дали е избрано поне едно правило
            const selectedRules = $(this.scanConfig.selectors.rulesCheckboxes + ':checked').length;
            if (selectedRules === 0) {
                this.showAlert('Моля изберете поне едно правило за сканиране!', 'warning');
                return false;
            }

            return true;
        },

        // Показване на резултатите от тестовото сканиране
        showTestResults: function(preview, stats) {
            let content = '<div class="test-scan-results">';
            
            // Статистики
            if (stats) {
                content += `
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="info-box">
                                <div class="info-box-content">
                                    <span class="info-box-text">Ще бъдат обработени</span>
                                    <span class="info-box-number">${stats.will_process || 0}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="info-box">
                                <div class="info-box-content">
                                    <span class="info-box-text">Очаквани маркирани</span>
                                    <span class="info-box-number">${stats.expected_flagged || 0}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Примерни резултати
            if (preview && preview.length > 0) {
                content += '<h5>Примерни резултати (първите 10):</h5>';
                content += '<div class="table-responsive">';
                content += '<table class="table table-striped table-sm">';
                content += '<thead><tr><th>ID</th><th>Email</th><th>IP</th><th>Причина</th></tr></thead>';
                content += '<tbody>';
                
                preview.forEach(customer => {
                    content += `
                        <tr>
                            <td>${customer.customer_id}</td>
                            <td>${customer.email}</td>
                            <td>${customer.ip}</td>
                            <td>${customer.reason}</td>
                        </tr>
                    `;
                });
                
                content += '</tbody></table></div>';
            } else {
                content += '<div class="alert alert-info">Няма потребители, които да бъдат маркирани с текущите настройки.</div>';
            }

            content += '</div>';

            $(this.scanConfig.selectors.testResultsContent).html(content);
            $(this.scanConfig.selectors.testResults).slideDown();
        },

        // Обновяване на статистиките преди сканиране
        updatePreScanStats: function() {
            const params = this.getScanParams();
            
            this.sendAjaxRequest('customer/fakedetector/scan/prestats', params, (response) => {
                if (response.stats) {
                    this.updateStatsDisplay(response.stats);
                }
            });
        },

        // Обновяване на показването на статистиките
        updateStatsDisplay: function(stats) {
            // Тук може да се обнови някой елемент в интерфейса със статистики
            this.log('Pre-scan stats updated:', stats);
        },

        // Обновяване на текста на бутона за сканиране
        updateScanButtonText: function() {
            const isTestMode = $(this.scanConfig.selectors.testModeCheckbox).is(':checked');
            const button = $(this.scanConfig.selectors.runButton);
            
            if (isTestMode) {
                button.html('<i class="fa fa-eye"></i> Тестово сканиране');
                button.removeClass('btn-danger').addClass('btn-primary');
            } else {
                button.html('<i class="fa fa-play"></i> Реално сканиране');
                button.removeClass('btn-primary').addClass('btn-danger');
            }
        },

        // Показване на прогрес
        showProgress: function(message) {
            $(this.scanConfig.selectors.scanStatus).text(message);
            $(this.scanConfig.selectors.scanProgress).slideDown();
        },

        // Скриване на прогрес
        hideProgress: function() {
            $(this.scanConfig.selectors.scanProgress).slideUp();
            this.resetProgress();
        },

        // Рестартиране на прогреса
        resetProgress: function() {
            this.scanConfig.progress.current = 0;
            this.scanConfig.progress.total = 0;
            $(this.scanConfig.selectors.progressBar).css('width', '0%');
            $(this.scanConfig.selectors.progressText).text('0%');
        },

        // Стартиране на симулация на прогрес
        startProgressSimulation: function() {
            const self = this;
            let progress = 0;
            
            this.scanConfig.progress.interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress > 90) progress = 90; // Не достигаме 100% докато не завърши
                
                self.updateProgress(progress, 100);
            }, 500);
        },

        // Спиране на симулацията на прогрес
        stopProgressSimulation: function() {
            if (this.scanConfig.progress.interval) {
                clearInterval(this.scanConfig.progress.interval);
                this.scanConfig.progress.interval = null;
            }
            this.updateProgress(100, 100); // Завършваме на 100%
        },

        // Обновяване на прогреса
        updateProgress: function(current, total) {
            const percentage = Math.round((current / total) * 100);
            $(this.scanConfig.selectors.progressBar).css('width', percentage + '%');
            $(this.scanConfig.selectors.progressText).text(percentage + '%');
        },

        // Показване на резултатите от сканирането
        showScanResults: function(details) {
            const content = `
                <div class="scan-results">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="info-box">
                                <div class="info-box-icon bg-blue">
                                    <i class="fa fa-users"></i>
                                </div>
                                <div class="info-box-content">
                                    <span class="info-box-text">Обработени</span>
                                    <span class="info-box-number">${details.processed_count || 0}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="info-box">
                                <div class="info-box-icon bg-yellow">
                                    <i class="fa fa-flag"></i>
                                </div>
                                <div class="info-box-content">
                                    <span class="info-box-text">Маркирани</span>
                                    <span class="info-box-number">${details.flagged_count || 0}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="info-box">
                                <div class="info-box-icon bg-green">
                                    <i class="fa fa-clock-o"></i>
                                </div>
                                <div class="info-box-content">
                                    <span class="info-box-text">Време</span>
                                    <span class="info-box-number">${details.execution_time || 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            const modal = this.createModal('Резултати от сканирането', content);
            modal.modal('show');
        }
    });

    // Инициализация при зареждане на DOM
    $(document).ready(function() {
        if (window.location.pathname.includes('manual') || window.location.pathname.includes('scan')) {
            ScanModule.initScan();
        }
    });

    // Експортиране на модула
    window.ScanModule = ScanModule;

})();
