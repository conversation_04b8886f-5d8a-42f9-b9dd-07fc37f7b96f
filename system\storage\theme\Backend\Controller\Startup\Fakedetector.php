<?php

namespace Theme25\Backend\Controller\Startup;

/**
 * Fake Detector Startup Controller
 * Инициализира модула при зареждане на Backend темата
 */
class Fakedetector extends \Theme25\Controller {

    private $helper;
    private $config_data;

    public function __construct($registry) {
        parent::__construct($registry, 'startup/fakedetector');
        $this->loadConfig();
        $this->initHelper();
    }

    /**
     * Зарежда конфигурацията на модула
     */
    private function loadConfig() {
        $config_file = DIR_SYSTEM . 'storage/theme/Backend/Config/fakedetector_config.php';
        
        if (file_exists($config_file)) {
            $this->config_data = include $config_file;
        } else {
            $this->config_data = $this->getDefaultConfig();
        }
    }

    /**
     * Инициализира helper класа
     */
    private function initHelper() {
        require_once DIR_SYSTEM . 'storage/theme/Backend/Helper/Fakedetectorhelper.php';
        $this->helper = new \Theme25\Backend\Helper\Fakedetectorhelper($this->db, $this->config);
    }

    /**
     * Основен метод за инициализация
     */
    public function index() {
        // Проверка дали модулът е инсталиран
        if (!$this->helper->isModuleInstalled()) {
            $this->log('Fake Detector module is not installed');
            return;
        }

        // Регистриране на модула в системата
        $this->registerModule();

        // Инициализация на cron задачи
        $this->initCronJobs();

        // Проверка за обновления
        $this->checkForUpdates();

        // Почистване на стари записи (ако е активирано)
        $this->cleanupOldRecords();

        $this->log('Fake Detector module initialized successfully');
    }

    /**
     * Регистрира модула в системата
     */
    private function registerModule() {
        // Добавяне в менюто (ако не е добавено)
        $this->addToMenu();

        // Регистриране на права за достъп
        $this->registerPermissions();

        // Регистриране на езикови файлове
        $this->registerLanguageFiles();
    }

    /**
     * Добавя модула в менюто
     */
    private function addToMenu() {
        // Тази функционалност може да се имплементира според нуждите
        // За сега само логваме
        $this->log('Menu registration for Fake Detector module');
    }

    /**
     * Регистрира правата за достъп
     */
    private function registerPermissions() {
        $permissions = $this->config_data['permissions'] ?? [];
        
        foreach ($permissions as $type => $route) {
            // Логика за регистриране на права
            $this->log("Registered permission: {$type} for route: {$route}");
        }
    }

    /**
     * Регистрира езиковите файлове
     */
    private function registerLanguageFiles() {
        $language_files = $this->config_data['language_files'] ?? [];
        
        foreach ($language_files as $key => $file) {
            // Логика за регистриране на езикови файлове
            $this->log("Registered language file: {$file}");
        }
    }

    /**
     * Инициализира cron задачите
     */
    private function initCronJobs() {
        $cron_jobs = $this->config_data['cron_jobs'] ?? [];
        
        foreach ($cron_jobs as $job_name => $job_config) {
            if ($job_config['enabled'] ?? false) {
                $this->scheduleCronJob($job_name, $job_config);
            }
        }
    }

    /**
     * Планира cron задача
     */
    private function scheduleCronJob($job_name, $job_config) {
        // Логика за планиране на cron задача
        $this->log("Scheduled cron job: {$job_name} with schedule: {$job_config['schedule']}");
        
        // Тук може да се добави логика за добавяне в cron таблица или файл
    }

    /**
     * Проверява за обновления на модула
     */
    private function checkForUpdates() {
        $current_version = $this->helper->getModuleVersion();
        $config_version = $this->config_data['module_info']['version'] ?? '1.0.0';
        
        if (version_compare($current_version, $config_version, '<')) {
            $this->performUpdate($current_version, $config_version);
        }
    }

    /**
     * Изпълнява обновление на модула
     */
    private function performUpdate($from_version, $to_version) {
        $this->log("Updating Fake Detector module from {$from_version} to {$to_version}");
        
        try {
            // Логика за обновление
            $this->runUpdateScripts($from_version, $to_version);
            
            // Обновяване на версията в настройките
            $this->helper->updateModuleVersion($to_version);
            
            $this->log("Successfully updated to version {$to_version}");
            
        } catch (Exception $e) {
            $this->log("Update failed: " . $e->getMessage());
        }
    }

    /**
     * Изпълнява скриптове за обновление
     */
    private function runUpdateScripts($from_version, $to_version) {
        // Примерни обновления според версията
        
        if (version_compare($from_version, '1.1.0', '<') && version_compare($to_version, '1.1.0', '>=')) {
            $this->updateTo110();
        }
        
        if (version_compare($from_version, '2.0.0', '<') && version_compare($to_version, '2.0.0', '>=')) {
            $this->updateTo200();
        }
    }

    /**
     * Обновление към версия 1.1.0
     */
    private function updateTo110() {
        // Добавяне на нови колони или таблици
        $sql = "ALTER TABLE " . DB_PREFIX . "fake_customer_log 
                ADD COLUMN IF NOT EXISTS `admin_notes` TEXT AFTER `reason`";
        $this->db->query($sql);
        
        $this->log("Updated database schema to 1.1.0");
    }

    /**
     * Обновление към версия 2.0.0
     */
    private function updateTo200() {
        // Мигриране към новата архитектура
        $this->migrateToTheme25Architecture();
        
        $this->log("Migrated to Theme25 architecture (2.0.0)");
    }

    /**
     * Мигрира към Theme25 архитектурата
     */
    private function migrateToTheme25Architecture() {
        // Логика за мигриране на стари данни
        // Обновяване на настройки
        // Преместване на файлове
    }

    /**
     * Почиства стари записи
     */
    private function cleanupOldRecords() {
        $cleanup_enabled = $this->helper->getSetting('auto_cleanup_enabled', false);
        
        if (!$cleanup_enabled) {
            return;
        }
        
        $last_cleanup = $this->helper->getSetting('last_cleanup', '');
        $cleanup_interval = $this->helper->getSetting('cleanup_interval_hours', 24);
        
        // Проверка дали е време за почистване
        if ($last_cleanup && (time() - strtotime($last_cleanup)) < ($cleanup_interval * 3600)) {
            return;
        }
        
        try {
            $deleted_count = $this->helper->cleanOldRecords();
            
            if ($deleted_count > 0) {
                $this->log("Cleaned up {$deleted_count} old records");
            }
            
            // Записване на времето на последното почистване
            $this->helper->setSetting('last_cleanup', date('Y-m-d H:i:s'));
            
        } catch (Exception $e) {
            $this->log("Cleanup failed: " . $e->getMessage());
        }
    }

    /**
     * Връща конфигурация по подразбиране
     */
    private function getDefaultConfig() {
        return [
            'module_info' => [
                'name' => 'Fake Detector',
                'version' => '2.0.0',
                'description' => 'Модул за откриване на фалшиви регистрации'
            ],
            'permissions' => [
                'access' => 'customer/fakedetector',
                'modify' => 'customer/fakedetector'
            ],
            'cron_jobs' => [],
            'language_files' => []
        ];
    }

    /**
     * Логва съобщение
     */
    private function log($message) {
        if ($this->config->get('config_error_log')) {
            $log = new \Log('fake_detector_startup.log');
            $log->write('[' . date('Y-m-d H:i:s') . '] ' . $message);
        }
    }

    /**
     * Метод за ръчна инициализация (за debugging)
     */
    public function manualInit() {
        $this->index();
        return [
            'status' => 'success',
            'message' => 'Fake Detector module manually initialized',
            'version' => $this->helper->getModuleVersion(),
            'installed' => $this->helper->isModuleInstalled()
        ];
    }

    /**
     * Получава статуса на модула
     */
    public function getStatus() {
        return [
            'installed' => $this->helper->isModuleInstalled(),
            'version' => $this->helper->getModuleVersion(),
            'config_loaded' => !empty($this->config_data),
            'helper_initialized' => isset($this->helper),
            'last_scan' => $this->helper->getSetting('last_scan', 'Never'),
            'total_flagged' => $this->getTotalFlagged(),
            'module_info' => $this->config_data['module_info'] ?? []
        ];
    }

    /**
     * Получава общия брой маркирани потребители
     */
    private function getTotalFlagged() {
        try {
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "fake_customer_log");
            return (int)$query->row['total'];
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Деструктор - почистване на ресурси
     */
    public function __destruct() {
        // Почистване на временни файлове или връзки ако е необходимо
    }
}
