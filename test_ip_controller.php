<?php
/**
 * Test script for IP Controller
 * Тестов скрипт за IP контролера
 */

// Simulate basic OpenCart environment
define('DIR_APPLICATION', __DIR__ . '/admin/');
define('DB_PREFIX', 'oc_');

// Mock classes for testing
class MockRequest {
    public $get = [];
    public $post = [];
    
    public function __construct($customer_id = 1) {
        $this->get['customer_id'] = $customer_id;
        $this->get['user_token'] = 'test_token';
    }
}

class MockUser {
    public function getId() { return 1; }
    public function hasPermission($type, $route) { return true; }
}

class MockCustomerModel {
    public function getCustomer($customer_id) {
        return ['customer_id' => $customer_id, 'firstname' => 'Test', 'lastname' => 'Customer'];
    }
    
    public function getIps($customer_id, $start = 0, $limit = 10) {
        // Simulate some IP data
        return [
            [
                'customer_ip_id' => 1,
                'ip' => '***********',
                'date_added' => '2025-01-15 10:30:00'
            ],
            [
                'customer_ip_id' => 2,
                'ip' => '********',
                'date_added' => '2025-01-14 15:45:00'
            ]
        ];
    }
    
    public function getTotalIps($customer_id) {
        return 2;
    }
    
    public function getTotalCustomersByIp($ip) {
        return 1;
    }
}

class MockController {
    public $request;
    public $user;
    public $customerModel;
    
    public function __construct() {
        $this->request = new MockRequest();
        $this->user = new MockUser();
        $this->customerModel = new MockCustomerModel();
    }
    
    public function requestGet($key, $default = null) {
        return isset($this->request->get[$key]) ? $this->request->get[$key] : $default;
    }
    
    public function hasPermission($type, $route) {
        return $this->user->hasPermission($type, $route);
    }
    
    public function loadModelAs($model, $alias) {
        $this->$alias = $this->customerModel;
    }
    
    public function dbQuery($sql) {
        // Mock database query
        return (object)['num_rows' => 0, 'rows' => [], 'row' => ['total' => 0]];
    }
    
    public function dbEscape($value) {
        return addslashes($value);
    }
}

// Test the IP controller
echo "<h1>IP Controller Test</h1>\n";
echo "<h2>Testing IP Address Display</h2>\n";

try {
    // Include the IP controller (we'll simulate it)
    $controller = new MockController();
    
    // Simulate the IP controller logic
    $customer_id = 1;
    
    echo "<h3>Customer ID: $customer_id</h3>\n";
    
    // Get IP data
    $ips = $controller->customerModel->getIps($customer_id);
    $total = $controller->customerModel->getTotalIps($customer_id);
    
    echo "<h3>IP Addresses Found: $total</h3>\n";
    
    if (empty($ips)) {
        echo '<p class="text-gray-500">Няма записани IP адреси за този клиент.</p>';
    } else {
        echo '<div class="space-y-3">';
        
        foreach ($ips as $ip) {
            $statusClass = 'bg-white border-gray-200';
            $statusText = 'Активен';
            $statusIcon = 'ri-check-circle-line text-green-600';
            $totalCustomers = $controller->customerModel->getTotalCustomersByIp($ip['ip']);
            
            echo '
                <div class="flex items-center justify-between p-4 border rounded-lg ' . $statusClass . '">
                    <div class="flex items-center space-x-3">
                        <i class="' . $statusIcon . '"></i>
                        <div>
                            <div class="font-medium text-gray-900">' . htmlspecialchars($ip['ip']) . '</div>
                            <div class="text-sm text-gray-500">
                                Добавен: ' . date('d.m.Y H:i', strtotime($ip['date_added'])) . ' | 
                                Общо клиенти с този IP: ' . (int)$totalCustomers . '
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">' . 
                        $statusText . '</span>
                    </div>
                </div>';
        }
        
        echo '</div>';
        
        echo '<div class="mt-4 text-sm text-gray-500 text-center">
            Показани ' . count($ips) . ' от ' . $total . ' IP адреса
        </div>';
    }
    
    echo "<h3>Test Completed Successfully!</h3>\n";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error: " . $e->getMessage() . "</h3>\n";
}

echo "<h2>Expected Output:</h2>\n";
echo "<p>The IP controller should display a list of IP addresses with:</p>\n";
echo "<ul>\n";
echo "<li>IP address</li>\n";
echo "<li>Date added</li>\n";
echo "<li>Total customers using this IP</li>\n";
echo "<li>Status (Active/Banned)</li>\n";
echo "</ul>\n";

echo "<h2>URL Test:</h2>\n";
echo "<p>The JavaScript should call: <code>/admin/customer/customer/ip?customer_id=1&user_token=test_token</code></p>\n";
echo "<p>This should return HTML content, not JSON.</p>\n";
?>
