<!-- Blacklist Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
  <div class="flex flex-col md:flex-row md:items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Черен списък</h1>
      <p class="text-gray-500 mt-1">Управление на забранени IP адреси и email домейни</p>
    </div>
    <div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
      <a href="{{ back_url }}" class="px-4 py-2 bg-gray-600 text-white rounded-button hover:bg-gray-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-arrow-left-line"></i>
        </div>
        <span>Назад</span>
      </a>
      <button type="button" id="button-add" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-add-line"></i>
        </div>
        <span>Добави</span>
      </button>
      <button type="button" id="button-remove-selected" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-delete-bin-line"></i>
        </div>
        <span>Премахни избраните</span>
      </button>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="p-6">
  <!-- Alert Messages -->
  {% if error_warning %}
  <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-error-warning-line text-red-500 mr-2"></i>
      <span class="text-red-700">{{ error_warning }}</span>
    </div>
  </div>
  {% endif %}

  {% if success %}
  <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-check-line text-green-500 mr-2"></i>
      <span class="text-green-700">{{ success }}</span>
    </div>
  </div>
  {% endif %}

  <!-- Add Form Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6" id="add-form" style="display: none;">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 flex items-center">
        <i class="ri-add-line mr-2"></i>
        Добавяне в черен списък
      </h3>
    </div>
    <div class="p-6">
      <form id="form-add-blacklist">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-add-type">Тип</label>
            <select name="type" id="input-add-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" required>
              <option value="">Изберете тип</option>
              <option value="ip">IP адрес</option>
              <option value="email">Email домейн</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-add-value">Стойност</label>
            <input type="text" name="value" id="input-add-value" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="IP адрес или email домейн" required />
            <p class="text-xs text-gray-500 mt-1">За IP: ***********, за email: example.com</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-add-note">Бележка</label>
            <input type="text" name="note" id="input-add-note" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Опционална бележка" />
          </div>
          <div class="flex items-end">
            <div class="flex gap-2 w-full">
              <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center">
                <i class="ri-add-line mr-2"></i>
                Добави
              </button>
              <button type="button" id="button-cancel-add" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center">
                <i class="ri-close-line mr-2"></i>
                Отказ
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Main Table Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 flex items-center">
        <i class="ri-forbid-line mr-2"></i>
        Записи в черния списък
      </h3>
    </div>
    <div class="p-6">


      <!-- Filters -->
      <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-filter-type">Тип</label>
            <select name="filter_type" id="input-filter-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
              {% for key, value in type_options %}
              <option value="{{ key }}"{% if filter_type == key %} selected{% endif %}>{{ value }}</option>
              {% endfor %}
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-filter-value">Стойност</label>
            <input type="text" name="filter_value" value="{{ filter_value }}" placeholder="IP адрес или email домейн" id="input-filter-value" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-filter-date-from">От дата</label>
            <div class="relative">
              <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="От дата" id="input-filter-date-from" class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <i class="ri-calendar-line text-gray-400"></i>
              </div>
            </div>
          </div>
          <div class="flex items-end">
            <div class="flex gap-2 w-full">
              <button type="button" id="button-filter" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center">
                <i class="ri-search-line mr-2"></i>
                Филтрирай
              </button>
              <button type="button" id="button-clear" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center">
                <i class="ri-refresh-line mr-2"></i>
                Изчисти
              </button>
            </div>
          </div>
        </div>
      </div>

        <!-- Таблица с резултати -->
        <form id="form-blacklist" method="post">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td class="text-left">
                    {% if sort == 'type' %}
                    <a href="{{ sort_type }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">Тип</a>
                    {% else %}
                    <a href="{{ sort_type }}">Тип</a>
                    {% endif %}
                  </td>
                  <td class="text-left">
                    {% if sort == 'value' %}
                    <a href="{{ sort_value }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">Стойност</a>
                    {% else %}
                    <a href="{{ sort_value }}">Стойност</a>
                    {% endif %}
                  </td>
                  <td class="text-left">Бележка</td>
                  <td class="text-center">Засегнати потребители</td>
                  <td class="text-left">
                    {% if sort == 'date_added' %}
                    <a href="{{ sort_date_added }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">Дата на добавяне</a>
                    {% else %}
                    <a href="{{ sort_date_added }}">Дата на добавяне</a>
                    {% endif %}
                  </td>
                  <td class="text-right">Действия</td>
                </tr>
              </thead>
              <tbody>
                {% if blacklist %}
                {% for item in blacklist %}
                <tr>
                  <td class="text-center">
                    <input type="checkbox" name="selected[]" value="{{ item.id }}" />
                  </td>
                  <td class="text-left">
                    {% if item.type == 'ip' %}
                    <span class="label label-info">{{ item.type_text }}</span>
                    {% else %}
                    <span class="label label-warning">{{ item.type_text }}</span>
                    {% endif %}
                  </td>
                  <td class="text-left">
                    <code>{{ item.value }}</code>
                  </td>
                  <td class="text-left">{{ item.note }}</td>
                  <td class="text-center">
                    {% if item.affected_customers > 0 %}
                    <span class="badge badge-danger">{{ item.affected_customers }}</span>
                    {% else %}
                    <span class="text-muted">0</span>
                    {% endif %}
                  </td>
                  <td class="text-left">{{ item.date_added }}</td>
                  <td class="text-right">
                    <button type="button" class="btn btn-danger btn-xs" onclick="removeBlacklistItem({{ item.id }})" data-toggle="tooltip" title="Премахни">
                      <i class="fa fa-trash"></i>
                    </button>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="7">Няма записи в черния списък.</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>

        <!-- Масови действия и пагинация -->
        {% if blacklist %}
        <div class="row">
          <div class="col-sm-6">
            <div class="btn-group">
              <button type="button" id="button-remove-selected" class="btn btn-danger">
                <i class="fa fa-trash"></i> Премахни избраните
              </button>
              <a href="{{ export_url }}" class="btn btn-info">
                <i class="fa fa-download"></i> Експорт
              </a>
              <a href="{{ import_url }}" class="btn btn-warning">
                <i class="fa fa-upload"></i> Импорт
              </a>
            </div>
          </div>
          <div class="col-sm-6 text-right">{{ pagination }}</div>
        </div>
        <div class="row">
          <div class="col-sm-6">
            <div class="text-muted">{{ results }}</div>
          </div>
        </div>
        {% endif %}

      </div>
    </div>
  </div>
</div>

{{ footer }}
