{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="button-add" class="btn btn-primary" data-toggle="tooltip" title="Добави в черен списък">
          <i class="fa fa-plus"></i> Добави
        </button>
        <a href="{{ back_url }}" class="btn btn-default" data-toggle="tooltip" title="Назад">
          <i class="fa fa-reply"></i> Назад
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-ban"></i> Черен списък</h3>
      </div>
      <div class="panel-body">
        
        <!-- Форма за добавяне -->
        <div class="well" id="add-form" style="display: none;">
          <h4>Добавяне в черен списък</h4>
          <form id="form-add-blacklist">
            <div class="row">
              <div class="col-sm-3">
                <div class="form-group">
                  <label class="control-label" for="input-add-type">Тип</label>
                  <select name="type" id="input-add-type" class="form-control" required>
                    <option value="">Изберете тип</option>
                    <option value="ip">IP адрес</option>
                    <option value="email">Email домейн</option>
                  </select>
                </div>
              </div>
              <div class="col-sm-4">
                <div class="form-group">
                  <label class="control-label" for="input-add-value">Стойност</label>
                  <input type="text" name="value" id="input-add-value" class="form-control" placeholder="IP адрес или email домейн" required />
                  <small class="help-block">За IP: ***********, за email: example.com</small>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label class="control-label" for="input-add-note">Бележка</label>
                  <input type="text" name="note" id="input-add-note" class="form-control" placeholder="Опционална бележка" />
                </div>
              </div>
              <div class="col-sm-2">
                <div class="form-group">
                  <label class="control-label">&nbsp;</label>
                  <div class="btn-group btn-group-sm" style="display: block;">
                    <button type="submit" class="btn btn-primary">
                      <i class="fa fa-plus"></i> Добави
                    </button>
                    <button type="button" id="button-cancel-add" class="btn btn-default">
                      <i class="fa fa-times"></i> Отказ
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Филтри -->
        <div class="well">
          <div class="row">
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-filter-type">Тип</label>
                <select name="filter_type" id="input-filter-type" class="form-control">
                  {% for key, value in type_options %}
                  <option value="{{ key }}"{% if filter_type == key %} selected{% endif %}>{{ value }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label" for="input-filter-value">Стойност</label>
                <input type="text" name="filter_value" value="{{ filter_value }}" placeholder="IP адрес или email домейн" id="input-filter-value" class="form-control" />
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-filter-date-from">От дата</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="От дата" id="input-filter-date-from" class="form-control" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-sm-2">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <div class="btn-group btn-group-sm" style="display: block;">
                  <button type="button" id="button-filter" class="btn btn-primary">
                    <i class="fa fa-search"></i> Филтрирай
                  </button>
                  <button type="button" id="button-clear" class="btn btn-default">
                    <i class="fa fa-refresh"></i> Изчисти
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Таблица с резултати -->
        <form id="form-blacklist" method="post">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td class="text-left">
                    {% if sort == 'type' %}
                    <a href="{{ sort_type }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">Тип</a>
                    {% else %}
                    <a href="{{ sort_type }}">Тип</a>
                    {% endif %}
                  </td>
                  <td class="text-left">
                    {% if sort == 'value' %}
                    <a href="{{ sort_value }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">Стойност</a>
                    {% else %}
                    <a href="{{ sort_value }}">Стойност</a>
                    {% endif %}
                  </td>
                  <td class="text-left">Бележка</td>
                  <td class="text-center">Засегнати потребители</td>
                  <td class="text-left">
                    {% if sort == 'date_added' %}
                    <a href="{{ sort_date_added }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">Дата на добавяне</a>
                    {% else %}
                    <a href="{{ sort_date_added }}">Дата на добавяне</a>
                    {% endif %}
                  </td>
                  <td class="text-right">Действия</td>
                </tr>
              </thead>
              <tbody>
                {% if blacklist %}
                {% for item in blacklist %}
                <tr>
                  <td class="text-center">
                    <input type="checkbox" name="selected[]" value="{{ item.id }}" />
                  </td>
                  <td class="text-left">
                    {% if item.type == 'ip' %}
                    <span class="label label-info">{{ item.type_text }}</span>
                    {% else %}
                    <span class="label label-warning">{{ item.type_text }}</span>
                    {% endif %}
                  </td>
                  <td class="text-left">
                    <code>{{ item.value }}</code>
                  </td>
                  <td class="text-left">{{ item.note }}</td>
                  <td class="text-center">
                    {% if item.affected_customers > 0 %}
                    <span class="badge badge-danger">{{ item.affected_customers }}</span>
                    {% else %}
                    <span class="text-muted">0</span>
                    {% endif %}
                  </td>
                  <td class="text-left">{{ item.date_added }}</td>
                  <td class="text-right">
                    <button type="button" class="btn btn-danger btn-xs" onclick="removeBlacklistItem({{ item.id }})" data-toggle="tooltip" title="Премахни">
                      <i class="fa fa-trash"></i>
                    </button>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="7">Няма записи в черния списък.</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>

        <!-- Масови действия и пагинация -->
        {% if blacklist %}
        <div class="row">
          <div class="col-sm-6">
            <div class="btn-group">
              <button type="button" id="button-remove-selected" class="btn btn-danger">
                <i class="fa fa-trash"></i> Премахни избраните
              </button>
              <a href="{{ export_url }}" class="btn btn-info">
                <i class="fa fa-download"></i> Експорт
              </a>
              <a href="{{ import_url }}" class="btn btn-warning">
                <i class="fa fa-upload"></i> Импорт
              </a>
            </div>
          </div>
          <div class="col-sm-6 text-right">{{ pagination }}</div>
        </div>
        <div class="row">
          <div class="col-sm-6">
            <div class="text-muted">{{ results }}</div>
          </div>
        </div>
        {% endif %}

      </div>
    </div>
  </div>
</div>

{{ footer }}
