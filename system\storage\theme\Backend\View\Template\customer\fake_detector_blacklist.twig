<!-- Blacklist Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
  <div class="flex flex-col md:flex-row md:items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Черен списък</h1>
      <p class="text-gray-500 mt-1">Управление на забранени IP адреси и email домейни</p>
    </div>
    <div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
      <a href="{{ back_url }}" class="px-4 py-2 bg-gray-600 text-white rounded-button hover:bg-gray-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-arrow-left-line"></i>
        </div>
        <span>Назад</span>
      </a>
      <button type="button" id="button-add" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-add-line"></i>
        </div>
        <span>Добави</span>
      </button>
      <button type="button" id="button-remove-selected" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-delete-bin-line"></i>
        </div>
        <span>Премахни избраните</span>
      </button>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="p-6">
  <!-- Alert Messages -->
  {% if error_warning %}
  <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-error-warning-line text-red-500 mr-2"></i>
      <span class="text-red-700">{{ error_warning }}</span>
    </div>
  </div>
  {% endif %}

  {% if success %}
  <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-check-line text-green-500 mr-2"></i>
      <span class="text-green-700">{{ success }}</span>
    </div>
  </div>
  {% endif %}

  <!-- Add Form Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6" id="add-form" style="display: none;">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 flex items-center">
        <i class="ri-add-line mr-2"></i>
        Добавяне в черен списък
      </h3>
    </div>
    <div class="p-6">
      <form id="form-add-blacklist">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-add-type">Тип</label>
            <select name="type" id="input-add-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" required>
              <option value="">Изберете тип</option>
              <option value="ip">IP адрес</option>
              <option value="email">Email домейн</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-add-value">Стойност</label>
            <input type="text" name="value" id="input-add-value" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="IP адрес или email домейн" required />
            <p class="text-xs text-gray-500 mt-1">За IP: ***********, за email: example.com</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-add-note">Бележка</label>
            <input type="text" name="note" id="input-add-note" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Опционална бележка" />
          </div>
          <div class="flex items-end">
            <div class="flex gap-2 w-full">
              <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center">
                <i class="ri-add-line mr-2"></i>
                Добави
              </button>
              <button type="button" id="button-cancel-add" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center">
                <i class="ri-close-line mr-2"></i>
                Отказ
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Main Table Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 flex items-center">
        <i class="ri-forbid-line mr-2"></i>
        Записи в черния списък
      </h3>
    </div>
    <div class="p-6">


      <!-- Filters -->
      <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-filter-type">Тип</label>
            <select name="filter_type" id="input-filter-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
              {% for key, value in type_options %}
              <option value="{{ key }}"{% if filter_type == key %} selected{% endif %}>{{ value }}</option>
              {% endfor %}
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-filter-value">Стойност</label>
            <input type="text" name="filter_value" value="{{ filter_value }}" placeholder="IP адрес или email домейн" id="input-filter-value" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-filter-date-from">От дата</label>
            <div class="relative">
              <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="От дата" id="input-filter-date-from" class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <i class="ri-calendar-line text-gray-400"></i>
              </div>
            </div>
          </div>
          <div class="flex items-end">
            <div class="flex gap-2 w-full">
              <button type="button" id="button-filter" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center">
                <i class="ri-search-line mr-2"></i>
                Филтрирай
              </button>
              <button type="button" id="button-clear" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center">
                <i class="ri-refresh-line mr-2"></i>
                Изчисти
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Results Table -->
      <form id="form-blacklist" method="post">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                  <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {% if sort == 'type' %}
                  <a href="{{ sort_type }}" class="flex items-center hover:text-gray-700">
                    Тип
                    <i class="ri-arrow-up-down-line ml-1 {% if order == 'ASC' %}text-primary{% else %}text-primary{% endif %}"></i>
                  </a>
                  {% else %}
                  <a href="{{ sort_type }}" class="flex items-center hover:text-gray-700">
                    Тип
                    <i class="ri-arrow-up-down-line ml-1"></i>
                  </a>
                  {% endif %}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {% if sort == 'value' %}
                  <a href="{{ sort_value }}" class="flex items-center hover:text-gray-700">
                    Стойност
                    <i class="ri-arrow-up-down-line ml-1 {% if order == 'ASC' %}text-primary{% else %}text-primary{% endif %}"></i>
                  </a>
                  {% else %}
                  <a href="{{ sort_value }}" class="flex items-center hover:text-gray-700">
                    Стойност
                    <i class="ri-arrow-up-down-line ml-1"></i>
                  </a>
                  {% endif %}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Бележка
                </th>
                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Засегнати потребители
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {% if sort == 'date_added' %}
                  <a href="{{ sort_date_added }}" class="flex items-center hover:text-gray-700">
                    Дата на добавяне
                    <i class="ri-arrow-up-down-line ml-1 {% if order == 'ASC' %}text-primary{% else %}text-primary{% endif %}"></i>
                  </a>
                  {% else %}
                  <a href="{{ sort_date_added }}" class="flex items-center hover:text-gray-700">
                    Дата на добавяне
                    <i class="ri-arrow-up-down-line ml-1"></i>
                  </a>
                  {% endif %}
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Действия
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% if blacklist %}
              {% for item in blacklist %}
              <tr class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <input type="checkbox" name="selected[]" value="{{ item.id }}" class="rounded border-gray-300 text-primary focus:ring-primary" />
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {% if item.type == 'ip' %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {{ item.type_text|default('IP адрес') }}
                  </span>
                  {% else %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                    {{ item.type_text|default('Email домейн') }}
                  </span>
                  {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <code class="px-2 py-1 bg-gray-100 rounded text-sm font-mono">{{ item.value }}</code>
                </td>
                <td class="px-6 py-4 text-sm text-gray-900">
                  <div class="max-w-xs truncate" title="{{ item.note }}">
                    {{ item.note|default('-') }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  {% if item.affected_customers > 0 %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    {{ item.affected_customers }}
                  </span>
                  {% else %}
                  <span class="text-gray-400">0</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ item.date_added }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button type="button" class="btn-remove-blacklist inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 transition-colors" data-id="{{ item.id }}" title="Премахни">
                    <i class="ri-delete-bin-line mr-1"></i>
                    Премахни
                  </button>
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                  <div class="flex flex-col items-center">
                    <i class="ri-forbid-line text-4xl text-gray-300 mb-2"></i>
                    <p class="text-lg font-medium">Няма записи в черния списък</p>
                    <p class="text-sm">Добавете IP адреси или email домейни за блокиране</p>
                  </div>
                </td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
      </form>

      <!-- Bulk Actions and Pagination -->
      {% if blacklist %}
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="flex gap-2">
            <button type="button" id="button-remove-selected" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 transition-colors">
              <i class="ri-delete-bin-line mr-2"></i>
              Премахни избраните
            </button>
            <a href="{{ export_url }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
              <i class="ri-download-line mr-2"></i>
              Експорт
            </a>
            <a href="{{ import_url }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 transition-colors">
              <i class="ri-upload-line mr-2"></i>
              Импорт
            </a>
          </div>
          <div class="flex items-center justify-between sm:justify-end gap-4">
            <div class="text-sm text-gray-700">
              {{ results }}
            </div>
            <div>
              {{ pagination }}
            </div>
          </div>
        </div>
      </div>
      {% endif %}

      </div>
    </div>
  </div>
</div>
