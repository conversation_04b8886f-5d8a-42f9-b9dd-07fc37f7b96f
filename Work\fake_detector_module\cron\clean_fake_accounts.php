<?php
require_once(dirname(__DIR__) . '/config.php');
$db = new mysqli(DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE);
if ($db->connect_error) {
    die("Грешка при свързване: " . $db->connect_error);
}
$rules = require_once(dirname(__DIR__) . '/system/fake_detector_rules.php');

$blacklist = ['ip' => [], 'email' => []];
$res = $db->query("SELECT * FROM oc_fake_blacklist");
while ($row = $res->fetch_assoc()) {
    $type = $row['type'];
    $val = strtolower(trim($row['value']));
    $blacklist[$type][] = $val;
}

$sql = "SELECT c.customer_id, c.firstname, c.email, c.ip, c.date_added FROM oc_customer c";
$result = $db->query($sql);
$flagged = [];

while ($row = $result->fetch_assoc()) {
    $reasons = [];
    $firstname = $row['firstname'];
    $email = strtolower($row['email']);
    $domain = substr(strrchr($email, "@"), 1);
    $ip = $row['ip'];
    $customer_id = (int)$row['customer_id'];

    if (strlen($firstname) >= $rules['min_username_length'] &&
        preg_match($rules['regex_username'], $firstname)) {
        $reasons[] = "Подозрително име";
    }

    if (in_array($domain, $rules['bad_domains'])) {
        $reasons[] = "Съмнителен имейл домейн ($domain)";
    }

    $ip_count = $db->query("SELECT COUNT(*) as total FROM oc_customer WHERE ip = '" . $db->real_escape_string($ip) . "'")->fetch_assoc()['total'];
    if ($ip_count >= $rules['max_accounts_per_ip']) {
        $reasons[] = "Многократни регистрации от IP ($ip_count)";
    }

    if ($rules['only_no_orders']) {
        $order_count = $db->query("SELECT COUNT(*) as total FROM oc_order WHERE customer_id = $customer_id")->fetch_assoc()['total'];
        if ($order_count == 0) {
            $reasons[] = "Няма поръчки";
        }
    }

    if ($rules['check_blacklist']) {
        if (in_array($ip, $blacklist['ip'])) {
            $reasons[] = "IP в черен списък";
        }
        if (in_array($email, $blacklist['email'])) {
            $reasons[] = "Имейл в черен списък";
        }
    }

    if (!empty($reasons)) {
        $flagged[] = [
            'customer_id' => $customer_id,
            'email' => $email,
            'ip' => $ip,
            'reason' => implode('; ', $reasons)
        ];
    }
}

foreach ($flagged as $entry) {
    $customer_id = (int)$entry['customer_id'];
    $check = $db->query("SELECT id FROM oc_fake_customer_log WHERE customer_id = $customer_id");
    if ($check->num_rows > 0) continue;

    $stmt = $db->prepare("INSERT INTO oc_fake_customer_log (customer_id, email, ip, reason, date_detected) VALUES (?, ?, ?, ?, NOW())");
    $stmt->bind_param('isss', $customer_id, $entry['email'], $entry['ip'], $entry['reason']);
    $stmt->execute();

    if (!$rules['test_mode']) {
        $db->query("DELETE FROM oc_customer WHERE customer_id = $customer_id");
        $db->query("DELETE FROM oc_address WHERE customer_id = $customer_id");
        $db->query("DELETE FROM oc_customer_activity WHERE customer_id = $customer_id");
        $db->query("DELETE FROM oc_customer_login WHERE customer_id = $customer_id");
    }
}
echo "Готово. Обработени са " . count($flagged) . " потребители.\n";
$db->close();
