<?php

namespace Theme25\Backend\Controller\Customer\Fakedetector;

class Scan extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('fake_detector_scan.log');
    }

    /**
     * Автоматично сканиране (адаптирано от оригиналния rescan)
     */
    public function execute() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            try {
                $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                
                // Изпълняване на автоматичното сканиране
                $result = $this->fakeDetectorModel->runScan();
                
                $json['success'] = sprintf(
                    'Сканирането приключи успешно! Маркирани са %d потребители.',
                    $result['flagged_count']
                );
                $json['details'] = $result;
                
            } catch (Exception $e) {
                $json['error'] = 'Грешка при сканиране: ' . $e->getMessage();
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Ръчно сканиране (адаптирано от fake_detector_manual_scan.php)
     */
    public function manual() {
        if ($this->isPostRequest()) {
            return $this->processManualScan();
        } else {
            return $this->showManualScanForm();
        }
    }

    /**
     * Показва формата за ръчно сканиране
     */
    private function showManualScanForm() {
        $this->setTitle('Ръчно сканиране');
        $this->initAdminData();
        
        $this->prepareManualScanData();
        $this->renderTemplateWithDataAndOutput('customer/fake_detector_manual_scan');
    }

    /**
     * Обработва ръчното сканиране
     */
    private function processManualScan() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            try {
                $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                
                // Получаване на параметрите за сканиране
                $scan_params = [
                    'test_mode' => $this->requestPost('test_mode', 1),
                    'customer_ids' => $this->requestPost('customer_ids', []),
                    'date_from' => $this->requestPost('date_from', ''),
                    'date_to' => $this->requestPost('date_to', ''),
                    'specific_rules' => $this->requestPost('rules', [])
                ];

                // Изпълняване на ръчното сканиране
                $result = $this->fakeDetectorModel->scanAccounts($scan_params);
                
                $json['success'] = sprintf(
                    'Ръчното сканиране приключи! Обработени са %d потребители, маркирани са %d.',
                    $result['processed_count'],
                    $result['flagged_count']
                );
                $json['details'] = $result;
                
            } catch (Exception $e) {
                $json['error'] = 'Грешка при ръчно сканиране: ' . $e->getMessage();
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Подготвя данните за формата за ръчно сканиране
     */
    private function prepareManualScanData() {
        $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
        
        // Получаване на настройките
        $settings = $this->fakeDetectorModel->getSettings();
        
        // Статистики за потребителите
        $customer_stats = $this->fakeDetectorModel->getCustomerStatistics();
        
        // Налични правила за сканиране
        $available_rules = [
            'suspicious_names' => 'Подозрителни имена',
            'bad_domains' => 'Съмнителни email домейни',
            'multiple_ip' => 'Множество регистрации от един IP',
            'no_orders' => 'Акаунти без поръчки',
            'blacklist_check' => 'Проверка в черен списък'
        ];

        $this->setData([
            'settings' => $settings,
            'customer_stats' => $customer_stats,
            'available_rules' => $available_rules,
            'action_url' => $this->getAdminLink('customer/fakedetector/manual'),
            'back_url' => $this->getAdminLink('customer/fakedetector')
        ]);

        return $this;
    }

    /**
     * AJAX метод за получаване на статистики преди сканиране
     */
    public function getPreScanStats() {
        $json = [];

        if (!$this->hasPermission('access', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за достъп!';
        } else {
            try {
                $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                
                $filters = [
                    'date_from' => $this->requestGet('date_from', ''),
                    'date_to' => $this->requestGet('date_to', ''),
                    'rules' => $this->requestGet('rules', [])
                ];

                $stats = $this->fakeDetectorModel->getPreScanStatistics($filters);
                $json['stats'] = $stats;
                
            } catch (Exception $e) {
                $json['error'] = 'Грешка при получаване на статистики: ' . $e->getMessage();
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за тестово сканиране (без запазване)
     */
    public function testScan() {
        $json = [];

        if (!$this->hasPermission('access', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за достъп!';
        } else {
            try {
                $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                
                $scan_params = [
                    'test_mode' => true, // Винаги тестов режим
                    'customer_ids' => $this->requestPost('customer_ids', []),
                    'date_from' => $this->requestPost('date_from', ''),
                    'date_to' => $this->requestPost('date_to', ''),
                    'specific_rules' => $this->requestPost('rules', [])
                ];

                $result = $this->fakeDetectorModel->testScanAccounts($scan_params);
                
                $json['success'] = 'Тестовото сканиране приключи успешно!';
                $json['preview'] = $result['preview'];
                $json['stats'] = $result['stats'];
                
            } catch (Exception $e) {
                $json['error'] = 'Грешка при тестово сканиране: ' . $e->getMessage();
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Планирано сканиране (cron функционалност)
     */
    public function scheduled() {
        $json = [];

        try {
            $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
            
            // Проверка дали автоматичното сканиране е активирано
            $settings = $this->fakeDetectorModel->getSettings();
            
            if (!$settings['auto_scan_enabled']) {
                $json['message'] = 'Автоматичното сканиране е деактивирано.';
                $this->setJSONResponseOutput($json);
                return;
            }

            // Проверка на интервала
            $last_scan = $this->fakeDetectorModel->getLastScanTime();
            $interval_hours = (int)$settings['scan_interval_hours'];
            
            if ($last_scan && (time() - strtotime($last_scan)) < ($interval_hours * 3600)) {
                $json['message'] = 'Сканирането не е необходимо още.';
                $this->setJSONResponseOutput($json);
                return;
            }

            // Изпълняване на планираното сканиране
            $result = $this->fakeDetectorModel->runScheduledScan();
            
            $json['success'] = sprintf(
                'Планираното сканиране приключи! Маркирани са %d потребители.',
                $result['flagged_count']
            );
            $json['details'] = $result;
            
        } catch (Exception $e) {
            $json['error'] = 'Грешка при планирано сканиране: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира параметрите за сканиране
     */
    private function validateScanParams($params) {
        $errors = [];

        if (!empty($params['date_from']) && !strtotime($params['date_from'])) {
            $errors[] = 'Невалидна начална дата.';
        }

        if (!empty($params['date_to']) && !strtotime($params['date_to'])) {
            $errors[] = 'Невалидна крайна дата.';
        }

        if (!empty($params['date_from']) && !empty($params['date_to'])) {
            if (strtotime($params['date_from']) > strtotime($params['date_to'])) {
                $errors[] = 'Началната дата не може да бъде по-късна от крайната.';
            }
        }

        return $errors;
    }
}
