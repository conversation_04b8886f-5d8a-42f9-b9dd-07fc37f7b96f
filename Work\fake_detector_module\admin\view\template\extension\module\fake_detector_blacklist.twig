<div class="container">
  <h2>Черен <PERSON>п<PERSON>ък</h2>

  {% if success %}
    <div class="alert alert-success">{{ success }}</div>
  {% endif %}

  <form action="{{ add_action }}" method="post" class="form-inline mb-3">
    <div class="form-group">
      <label for="type">Тип:</label>
      <select name="type" class="form-control mx-2">
        <option value="email">Email</option>
        <option value="ip">IP</option>
      </select>
    </div>
    <div class="form-group mx-2">
      <input type="text" name="value" class="form-control" placeholder="Стойност (email или IP)" required />
    </div>
    <button type="submit" class="btn btn-primary">Добави</button>
  </form>

  {% if blacklist %}
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Тип</th>
          <th>Стойност</th>
          <th>Дата</th>
          <th>Действия</th>
        </tr>
      </thead>
      <tbody>
        {% for entry in blacklist %}
          <tr>
            <td>{{ entry.type }}</td>
            <td>{{ entry.value }}</td>
            <td>{{ entry.date_added }}</td>
            <td>
              <a href="{{ delete_url }}&blacklist_id={{ entry.blacklist_id }}" class="btn btn-danger btn-sm" onclick="return confirm('Сигурен ли си?')">Премахни</a>
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% else %}
    <p>Няма записи в черния списък.</p>
  {% endif %}
</div>
