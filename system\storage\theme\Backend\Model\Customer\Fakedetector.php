<?php

namespace Theme25\Backend\Model\Customer;

class Fakedetector extends \Model {

    /**
     * Изпълнява автоматично сканиране за фалшиви регистрации
     * (адаптирано от оригиналния runScan метод)
     */
    public function runScan() {
        // Зареждане на правилата за детекция
        $rules = $this->getDetectionRules();
        
        // Зареждане на черния списък
        $blacklist = $this->loadBlacklist();
        
        $flagged_count = 0;
        $processed_count = 0;
        
        // Получаване на всички потребители
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer ORDER BY customer_id");
        
        foreach ($query->rows as $customer) {
            $processed_count++;
            $reasons = [];
            
            $firstname = $customer['firstname'];
            $email = strtolower($customer['email']);
            $domain = substr(strrchr($email, "@"), 1);
            $ip = $customer['ip'];
            $customer_id = (int)$customer['customer_id'];

            // Проверка за подозрителни имена
            if ($this->checkSuspiciousName($firstname, $rules)) {
                $reasons[] = "Подозрително име";
            }

            // Проверка за съмнителни домейни
            if ($this->checkBadDomain($domain, $rules)) {
                $reasons[] = "Съмнителен домейн ($domain)";
            }

            // Проверка за множество регистрации от един IP
            if ($this->checkMultipleIpRegistrations($ip, $rules)) {
                $ip_count = $this->getIpRegistrationCount($ip);
                $reasons[] = "IP с много регистрации ($ip_count)";
            }

            // Проверка за акаунти без поръчки
            if ($rules['only_no_orders'] && $this->checkNoOrders($customer_id)) {
                $reasons[] = "Няма поръчки";
            }

            // Проверка в черния списък
            if ($rules['check_blacklist'] && $this->checkBlacklist($ip, $email, $blacklist)) {
                if (in_array($ip, $blacklist['ip'])) {
                    $reasons[] = "IP в черен списък";
                }
                if (in_array($email, $blacklist['email']) || in_array($domain, $blacklist['email'])) {
                    $reasons[] = "Имейл в черен списък";
                }
            }

            // Ако има причини за маркиране
            if (!empty($reasons)) {
                if ($this->addFlaggedCustomer($customer_id, $email, $ip, $reasons)) {
                    $flagged_count++;
                }
            }
        }

        return [
            'processed_count' => $processed_count,
            'flagged_count' => $flagged_count,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Получава маркираните потребители с филтриране
     */
    public function getFlaggedCustomers($filter_data = []) {
        $sql = "SELECT fcl.*, c.firstname, c.lastname 
                FROM " . DB_PREFIX . "fake_customer_log fcl 
                LEFT JOIN " . DB_PREFIX . "customer c ON fcl.customer_id = c.customer_id 
                WHERE 1=1";

        // Прилагане на филтри
        if (!empty($filter_data['filter_status'])) {
            $sql .= " AND fcl.status = '" . $this->db->escape($filter_data['filter_status']) . "'";
        }

        if (!empty($filter_data['filter_reason'])) {
            $sql .= " AND fcl.reason LIKE '%" . $this->db->escape($filter_data['filter_reason']) . "%'";
        }

        if (!empty($filter_data['filter_email'])) {
            $sql .= " AND fcl.email LIKE '%" . $this->db->escape($filter_data['filter_email']) . "%'";
        }

        if (!empty($filter_data['filter_ip'])) {
            $sql .= " AND fcl.ip LIKE '%" . $this->db->escape($filter_data['filter_ip']) . "%'";
        }

        if (!empty($filter_data['filter_date_from'])) {
            $sql .= " AND DATE(fcl.date_detected) >= '" . $this->db->escape($filter_data['filter_date_from']) . "'";
        }

        if (!empty($filter_data['filter_date_to'])) {
            $sql .= " AND DATE(fcl.date_detected) <= '" . $this->db->escape($filter_data['filter_date_to']) . "'";
        }

        // Сортиране
        $sort = isset($filter_data['sort']) ? $filter_data['sort'] : 'date_detected';
        $order = isset($filter_data['order']) ? $filter_data['order'] : 'DESC';
        $sql .= " ORDER BY " . $sort . " " . $order;

        // Лимитиране
        if (isset($filter_data['start']) && isset($filter_data['limit'])) {
            $sql .= " LIMIT " . (int)$filter_data['start'] . "," . (int)$filter_data['limit'];
        }

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * Получава общия брой маркирани потребители
     */
    public function getTotalFlaggedCustomers($filter_data = []) {
        $sql = "SELECT COUNT(*) as total 
                FROM " . DB_PREFIX . "fake_customer_log fcl 
                LEFT JOIN " . DB_PREFIX . "customer c ON fcl.customer_id = c.customer_id 
                WHERE 1=1";

        // Прилагане на същите филтри
        if (!empty($filter_data['filter_status'])) {
            $sql .= " AND fcl.status = '" . $this->db->escape($filter_data['filter_status']) . "'";
        }

        if (!empty($filter_data['filter_reason'])) {
            $sql .= " AND fcl.reason LIKE '%" . $this->db->escape($filter_data['filter_reason']) . "%'";
        }

        if (!empty($filter_data['filter_email'])) {
            $sql .= " AND fcl.email LIKE '%" . $this->db->escape($filter_data['filter_email']) . "%'";
        }

        if (!empty($filter_data['filter_ip'])) {
            $sql .= " AND fcl.ip LIKE '%" . $this->db->escape($filter_data['filter_ip']) . "%'";
        }

        if (!empty($filter_data['filter_date_from'])) {
            $sql .= " AND DATE(fcl.date_detected) >= '" . $this->db->escape($filter_data['filter_date_from']) . "'";
        }

        if (!empty($filter_data['filter_date_to'])) {
            $sql .= " AND DATE(fcl.date_detected) <= '" . $this->db->escape($filter_data['filter_date_to']) . "'";
        }

        $query = $this->db->query($sql);
        return $query->row['total'];
    }

    /**
     * Одобрява маркиран потребител
     */
    public function approveCustomer($customer_id) {
        $sql = "UPDATE " . DB_PREFIX . "fake_customer_log 
                SET status = 'approved' 
                WHERE customer_id = " . (int)$customer_id;
        
        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }

    /**
     * Одобрява маркиран потребител с бележка
     */
    public function approveCustomerWithNote($customer_id, $note = '') {
        $sql = "UPDATE " . DB_PREFIX . "fake_customer_log
                SET status = 'approved',
                    approval_note = '" . $this->db->escape($note) . "',
                    approval_date = NOW()
                WHERE customer_id = " . (int)$customer_id;

        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }

    /**
     * Отменя одобрение на потребител
     */
    public function revokeApproval($customer_id, $reason = '') {
        $sql = "UPDATE " . DB_PREFIX . "fake_customer_log
                SET status = 'pending',
                    approval_note = '" . $this->db->escape($reason) . "',
                    approval_date = NULL
                WHERE customer_id = " . (int)$customer_id;

        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }

    /**
     * Получава информация за одобрение
     */
    public function getApprovalInfo($customer_id) {
        $query = $this->db->query("SELECT fcl.*, c.firstname, c.lastname, c.email
                                  FROM " . DB_PREFIX . "fake_customer_log fcl
                                  LEFT JOIN " . DB_PREFIX . "customer c ON fcl.customer_id = c.customer_id
                                  WHERE fcl.customer_id = " . (int)$customer_id);

        return $query->num_rows ? $query->row : null;
    }

    /**
     * Изтрива потребител и всички свързани данни
     * (адаптирано от оригиналния deleteCustomer метод)
     */
    public function deleteCustomer($customer_id) {
        $customer_id = (int)$customer_id;
        
        try {
            // Започваме транзакция
            $this->db->query("START TRANSACTION");
            
            // Изтриване на потребителя
            $this->db->query("DELETE FROM " . DB_PREFIX . "customer WHERE customer_id = " . $customer_id);
            
            // Изтриване на адресите
            $this->db->query("DELETE FROM " . DB_PREFIX . "address WHERE customer_id = " . $customer_id);
            
            // Изтриване на активността
            $this->db->query("DELETE FROM " . DB_PREFIX . "customer_activity WHERE customer_id = " . $customer_id);
            
            // Изтриване на login записите
            $this->db->query("DELETE FROM " . DB_PREFIX . "customer_login WHERE customer_id = " . $customer_id);
            
            // Изтриване на IP записите (ако съществуват)
            $this->db->query("DELETE FROM " . DB_PREFIX . "customer_ip WHERE customer_id = " . $customer_id);
            
            // Изтриване на транзакциите
            $this->db->query("DELETE FROM " . DB_PREFIX . "customer_transaction WHERE customer_id = " . $customer_id);
            
            // Изтриване на reward точките
            $this->db->query("DELETE FROM " . DB_PREFIX . "customer_reward WHERE customer_id = " . $customer_id);
            
            // Обновяване на статуса в лога
            $this->db->query("UPDATE " . DB_PREFIX . "fake_customer_log 
                             SET status = 'deleted' 
                             WHERE customer_id = " . $customer_id);
            
            // Потвърждаваме транзакцията
            $this->db->query("COMMIT");
            
            return true;
            
        } catch (Exception $e) {
            // Отменяме транзакцията при грешка
            $this->db->query("ROLLBACK");
            return false;
        }
    }

    /**
     * Изтрива потребител с причина
     */
    public function deleteCustomerWithReason($customer_id, $reason = '') {
        $customer_id = (int)$customer_id;

        // Първо маркираме в лога
        $sql = "UPDATE " . DB_PREFIX . "fake_customer_log
                SET status = 'deleted',
                    deletion_reason = '" . $this->db->escape($reason) . "',
                    deletion_date = NOW()
                WHERE customer_id = " . $customer_id;

        $this->db->query($sql);

        // След това изтриваме потребителя
        return $this->deleteCustomer($customer_id);
    }

    /**
     * Възстановява изтрит потребител
     */
    public function restoreCustomer($customer_id) {
        $sql = "UPDATE " . DB_PREFIX . "fake_customer_log
                SET status = 'pending',
                    deletion_reason = NULL,
                    deletion_date = NULL
                WHERE customer_id = " . (int)$customer_id;

        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }

    /**
     * Получава черния списък
     */
    public function getBlacklist($filter_data = []) {
        $sql = "SELECT * FROM " . DB_PREFIX . "fake_blacklist WHERE 1=1";

        if (!empty($filter_data['filter_type'])) {
            $sql .= " AND type = '" . $this->db->escape($filter_data['filter_type']) . "'";
        }

        if (!empty($filter_data['filter_value'])) {
            $sql .= " AND value LIKE '%" . $this->db->escape($filter_data['filter_value']) . "%'";
        }

        if (!empty($filter_data['filter_date_from'])) {
            $sql .= " AND DATE(date_added) >= '" . $this->db->escape($filter_data['filter_date_from']) . "'";
        }

        if (!empty($filter_data['filter_date_to'])) {
            $sql .= " AND DATE(date_added) <= '" . $this->db->escape($filter_data['filter_date_to']) . "'";
        }

        $sort = isset($filter_data['sort']) ? $filter_data['sort'] : 'date_added';
        $order = isset($filter_data['order']) ? $filter_data['order'] : 'DESC';
        $sql .= " ORDER BY " . $sort . " " . $order;

        if (isset($filter_data['start']) && isset($filter_data['limit'])) {
            $sql .= " LIMIT " . (int)$filter_data['start'] . "," . (int)$filter_data['limit'];
        }

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * Добавя запис в черния списък
     */
    public function addToBlacklist($type, $value, $note = '') {
        $sql = "INSERT IGNORE INTO " . DB_PREFIX . "fake_blacklist 
                (type, value, note, date_added) 
                VALUES (
                    '" . $this->db->escape($type) . "',
                    '" . $this->db->escape(strtolower($value)) . "',
                    '" . $this->db->escape($note) . "',
                    NOW()
                )";
        
        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }

    /**
     * Премахва запис от черния списък
     */
    public function removeFromBlacklist($id) {
        $sql = "DELETE FROM " . DB_PREFIX . "fake_blacklist WHERE id = " . (int)$id;
        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }

    /**
     * Получава настройките на модула
     */
    public function getSettings() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "fake_detector_settings");
        
        $settings = [];
        foreach ($query->rows as $row) {
            $value = $row['setting_value'];
            
            // Опит за декодиране на JSON
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $value = $decoded;
            }
            
            $settings[$row['setting_key']] = $value;
        }
        
        // Връщане на настройки по подразбиране ако няма записани
        return array_merge($this->getDefaultSettings(), $settings);
    }

    /**
     * Връща настройките по подразбиране
     */
    private function getDefaultSettings() {
        return [
            'min_username_length' => 10,
            'regex_username' => '/^[a-zA-Z0-9]{10,}$/',
            'bad_domains' => ['hotmail.com', 'mail.ru', 'yopmail.com', '10minutemail.com'],
            'max_accounts_per_ip' => 3,
            'only_no_orders' => true,
            'check_blacklist' => true,
            'test_mode' => true,
            'auto_scan_enabled' => false,
            'scan_interval_hours' => 24
        ];
    }

    /**
     * Получава правилата за детекция
     */
    private function getDetectionRules() {
        $settings = $this->getSettings();
        return [
            'min_username_length' => (int)$settings['min_username_length'],
            'regex_username' => $settings['regex_username'],
            'bad_domains' => is_array($settings['bad_domains']) ? $settings['bad_domains'] : [],
            'max_accounts_per_ip' => (int)$settings['max_accounts_per_ip'],
            'only_no_orders' => (bool)$settings['only_no_orders'],
            'check_blacklist' => (bool)$settings['check_blacklist'],
            'test_mode' => (bool)$settings['test_mode']
        ];
    }

    /**
     * Зарежда черния списък в масиви
     */
    private function loadBlacklist() {
        $blacklist = ['ip' => [], 'email' => []];

        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "fake_blacklist");
        foreach ($query->rows as $row) {
            $blacklist[$row['type']][] = strtolower($row['value']);
        }

        return $blacklist;
    }

    /**
     * Проверява за подозрителни имена
     */
    private function checkSuspiciousName($firstname, $rules) {
        if (strlen($firstname) >= $rules['min_username_length'] &&
            preg_match($rules['regex_username'], $firstname)) {
            return true;
        }
        return false;
    }

    /**
     * Проверява за съмнителни домейни
     */
    private function checkBadDomain($domain, $rules) {
        return in_array($domain, $rules['bad_domains']);
    }

    /**
     * Проверява за множество регистрации от един IP
     */
    private function checkMultipleIpRegistrations($ip, $rules) {
        $count = $this->getIpRegistrationCount($ip);
        return $count >= $rules['max_accounts_per_ip'];
    }

    /**
     * Получава броя регистрации от IP адрес
     */
    private function getIpRegistrationCount($ip) {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "customer WHERE ip = '" . $this->db->escape($ip) . "'");
        return (int)$query->row['total'];
    }

    /**
     * Проверява дали потребителят няма поръчки
     */
    private function checkNoOrders($customer_id) {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "order WHERE customer_id = " . (int)$customer_id);
        return (int)$query->row['total'] === 0;
    }

    /**
     * Проверява в черния списък
     */
    private function checkBlacklist($ip, $email, $blacklist) {
        $domain = substr(strrchr($email, "@"), 1);

        return in_array($ip, $blacklist['ip']) ||
               in_array($email, $blacklist['email']) ||
               in_array($domain, $blacklist['email']);
    }

    /**
     * Добавя маркиран потребител в лога
     */
    private function addFlaggedCustomer($customer_id, $email, $ip, $reasons) {
        // Проверка дали вече съществува
        $exists = $this->db->query("SELECT * FROM " . DB_PREFIX . "fake_customer_log WHERE customer_id = " . (int)$customer_id);

        if (!$exists->num_rows) {
            $sql = "INSERT INTO " . DB_PREFIX . "fake_customer_log
                    (customer_id, email, ip, reason, date_detected)
                    VALUES (
                        " . (int)$customer_id . ",
                        '" . $this->db->escape($email) . "',
                        '" . $this->db->escape($ip) . "',
                        '" . $this->db->escape(implode('; ', $reasons)) . "',
                        NOW()
                    )";

            $this->db->query($sql);
            return $this->db->countAffected() > 0;
        }

        return false;
    }

    /**
     * Получава маркиран потребител по ID
     */
    public function getFlaggedCustomer($customer_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "fake_customer_log WHERE customer_id = " . (int)$customer_id);
        return $query->num_rows ? $query->row : null;
    }

    /**
     * Получава общия брой записи в черния списък
     */
    public function getTotalBlacklistItems($filter_data = []) {
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "fake_blacklist WHERE 1=1";

        if (!empty($filter_data['filter_type'])) {
            $sql .= " AND type = '" . $this->db->escape($filter_data['filter_type']) . "'";
        }

        if (!empty($filter_data['filter_value'])) {
            $sql .= " AND value LIKE '%" . $this->db->escape($filter_data['filter_value']) . "%'";
        }

        $query = $this->db->query($sql);
        return $query->row['total'];
    }

    /**
     * Проверява дали запис в черния списък съществува
     */
    public function blacklistEntryExists($type, $value) {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "fake_blacklist
                                  WHERE type = '" . $this->db->escape($type) . "'
                                  AND value = '" . $this->db->escape(strtolower($value)) . "'");
        return $query->row['total'] > 0;
    }

    /**
     * Получава броя засегнати потребители от запис в черния списък
     */
    public function getAffectedCustomersCount($type, $value) {
        if ($type === 'ip') {
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "customer WHERE ip = '" . $this->db->escape($value) . "'");
        } else {
            // За email домейни
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "customer WHERE email LIKE '%@" . $this->db->escape($value) . "'");
        }

        return (int)$query->row['total'];
    }

    /**
     * Получава най-честите причини за маркиране
     */
    public function getCommonReasons() {
        $query = $this->db->query("SELECT reason, COUNT(*) as count
                                  FROM " . DB_PREFIX . "fake_customer_log
                                  GROUP BY reason
                                  ORDER BY count DESC
                                  LIMIT 10");
        return $query->rows;
    }

    /**
     * Получава общи статистики за потребителите
     * Необходимо за ръчното сканиране - показва състоянието на системата
     */
    public function getCustomerStatistics() {
        $stats = [];

        // Общо потребители в системата
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "customer");
        $stats['total_customers'] = (int)$query->row['total'];

        // Маркирани потребители по статуси
        $query = $this->db->query("SELECT status, COUNT(*) as count
                                  FROM " . DB_PREFIX . "fake_customer_log
                                  GROUP BY status");

        $stats['flagged_customers'] = 0;
        $stats['approved_customers'] = 0;
        $stats['deleted_customers'] = 0;
        $stats['pending_customers'] = 0;

        foreach ($query->rows as $row) {
            switch ($row['status']) {
                case 'pending':
                    $stats['pending_customers'] = (int)$row['count'];
                    break;
                case 'approved':
                    $stats['approved_customers'] = (int)$row['count'];
                    break;
                case 'deleted':
                    $stats['deleted_customers'] = (int)$row['count'];
                    break;
            }
            $stats['flagged_customers'] += (int)$row['count'];
        }

        // Потребители без поръчки (потенциални цели за сканиране)
        $query = $this->db->query("SELECT COUNT(DISTINCT c.customer_id) as total
                                  FROM " . DB_PREFIX . "customer c
                                  LEFT JOIN " . DB_PREFIX . "order o ON c.customer_id = o.customer_id
                                  WHERE o.customer_id IS NULL");
        $stats['customers_no_orders'] = (int)$query->row['total'];

        // Потребители регистрирани в последните 30 дни
        $query = $this->db->query("SELECT COUNT(*) as total
                                  FROM " . DB_PREFIX . "customer
                                  WHERE date_added >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $stats['recent_customers'] = (int)$query->row['total'];

        // Записи в черния списък
        $query = $this->db->query("SELECT type, COUNT(*) as count
                                  FROM " . DB_PREFIX . "fake_blacklist
                                  GROUP BY type");

        $stats['blacklist_ip'] = 0;
        $stats['blacklist_email'] = 0;

        foreach ($query->rows as $row) {
            if ($row['type'] === 'ip') {
                $stats['blacklist_ip'] = (int)$row['count'];
            } elseif ($row['type'] === 'email') {
                $stats['blacklist_email'] = (int)$row['count'];
            }
        }

        // Последно сканиране
        $query = $this->db->query("SELECT MAX(date_detected) as last_scan
                                  FROM " . DB_PREFIX . "fake_customer_log");
        $stats['last_scan'] = $query->row['last_scan'] ?? null;

        return $stats;
    }

    /**
     * Изпълнява ръчно сканиране с персонализирани параметри
     */
    public function scanAccounts($params = []) {
        $test_mode = isset($params['test_mode']) ? (bool)$params['test_mode'] : true;
        $date_from = $params['date_from'] ?? null;
        $date_to = $params['date_to'] ?? null;
        $customer_ids = $params['customer_ids'] ?? [];
        $rules = $params['rules'] ?? [];

        // Зареждане на helper класа за правилата (autoload)
        $helper = new \Theme25\Backend\Helper\Fakedetectorhelper($this->db);

        $results = [
            'processed_count' => 0,
            'flagged_count' => 0,
            'flagged_customers' => [],
            'execution_time' => 0
        ];

        $start_time = microtime(true);

        // Построяване на SQL заявката за потребители
        $sql = "SELECT * FROM " . DB_PREFIX . "customer WHERE 1=1";

        // Филтриране по дати
        if ($date_from) {
            $sql .= " AND date_added >= '" . $this->db->escape($date_from) . "'";
        }
        if ($date_to) {
            $sql .= " AND date_added <= '" . $this->db->escape($date_to) . "'";
        }

        // Филтриране по конкретни потребители
        if (!empty($customer_ids)) {
            $ids = array_map('intval', $customer_ids);
            $sql .= " AND customer_id IN (" . implode(',', $ids) . ")";
        }

        $sql .= " ORDER BY customer_id";

        $query = $this->db->query($sql);
        $customers = $query->rows;

        foreach ($customers as $customer) {
            $results['processed_count']++;

            // Анализиране на потребителя с избраните правила
            $reasons = [];

            if (in_array('suspicious_names', $rules) && $helper->isSuspiciousName($customer['firstname'])) {
                $reasons[] = 'Подозрително име';
            }

            if (in_array('bad_domains', $rules)) {
                $domain = substr(strrchr($customer['email'], "@"), 1);
                if ($helper->isBadDomain($domain)) {
                    $reasons[] = "Съмнителен домейн ($domain)";
                }
            }

            if (in_array('multiple_ip', $rules) && $helper->hasMultipleRegistrations($customer['ip'])) {
                $count = $helper->getIpRegistrationCount($customer['ip']);
                $reasons[] = "IP с много регистрации ($count)";
            }

            if (in_array('no_orders', $rules) && $helper->hasNoOrders($customer['customer_id'])) {
                $reasons[] = 'Няма поръчки';
            }

            if (in_array('blacklist_check', $rules) && $helper->isInBlacklist($customer['ip'], $customer['email'])) {
                $reasons[] = 'В черен списък';
            }

            // Ако има причини за маркиране
            if (!empty($reasons)) {
                $results['flagged_count']++;

                $flagged_customer = [
                    'customer_id' => $customer['customer_id'],
                    'email' => $customer['email'],
                    'ip' => $customer['ip'],
                    'reason' => implode(', ', $reasons),
                    'date_detected' => date('Y-m-d H:i:s')
                ];

                $results['flagged_customers'][] = $flagged_customer;

                // Ако не е тестов режим, записваме в базата
                if (!$test_mode) {
                    $this->db->query("INSERT IGNORE INTO " . DB_PREFIX . "fake_customer_log
                                     (customer_id, email, ip, reason, date_detected, status)
                                     VALUES (
                                         " . (int)$customer['customer_id'] . ",
                                         '" . $this->db->escape($customer['email']) . "',
                                         '" . $this->db->escape($customer['ip']) . "',
                                         '" . $this->db->escape(implode(', ', $reasons)) . "',
                                         NOW(),
                                         'pending'
                                     )");
                }
            }
        }

        $results['execution_time'] = round(microtime(true) - $start_time, 2) . 's';

        return $results;
    }

    /**
     * Запазва настройка в базата данни
     */
    public function saveSetting($key, $value) {
        // Конвертиране на стойността в подходящ формат
        if (is_array($value)) {
            $value = json_encode($value);
        } elseif (is_bool($value)) {
            $value = $value ? '1' : '0';
        }

        $this->db->query("INSERT INTO " . DB_PREFIX . "fake_detector_settings
                         (setting_key, setting_value)
                         VALUES (
                             '" . $this->db->escape($key) . "',
                             '" . $this->db->escape($value) . "'
                         )
                         ON DUPLICATE KEY UPDATE
                         setting_value = '" . $this->db->escape($value) . "'");
    }

    /**
     * Получава настройка от базата данни
     */
    public function getSetting($key, $default = null) {
        $query = $this->db->query("SELECT setting_value FROM " . DB_PREFIX . "fake_detector_settings
                                  WHERE setting_key = '" . $this->db->escape($key) . "'");

        if ($query->num_rows) {
            $value = $query->row['setting_value'];

            // Опит за декодиране на JSON
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $decoded;
            }

            // Конвертиране на булеви стойности
            if ($value === '1' || $value === '0') {
                return (bool)$value;
            }

            return $value;
        }

        return $default;
    }

    /**
     * Изтрива настройка от базата данни
     */
    public function deleteSetting($key) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "fake_detector_settings
                         WHERE setting_key = '" . $this->db->escape($key) . "'");
        return $this->db->countAffected() > 0;
    }

    /**
     * Получава всички настройки като асоциативен масив
     */
    public function getAllSettings() {
        $query = $this->db->query("SELECT setting_key, setting_value FROM " . DB_PREFIX . "fake_detector_settings");

        $settings = [];
        foreach ($query->rows as $row) {
            $value = $row['setting_value'];

            // Опит за декодиране на JSON
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $settings[$row['setting_key']] = $decoded;
            } elseif ($value === '1' || $value === '0') {
                $settings[$row['setting_key']] = (bool)$value;
            } else {
                $settings[$row['setting_key']] = $value;
            }
        }

        return $settings;
    }

    /**
     * Експортира всички настройки като JSON
     */
    public function exportSettings() {
        $settings = $this->getAllSettings();

        $export_data = [
            'version' => '1.0',
            'export_date' => date('Y-m-d H:i:s'),
            'settings' => $settings
        ];

        return json_encode($export_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Импортира настройки от JSON
     */
    public function importSettings($json_data) {
        $data = json_decode($json_data, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Невалиден JSON формат');
        }

        if (!isset($data['settings']) || !is_array($data['settings'])) {
            throw new Exception('Невалидна структура на файла');
        }

        $imported_count = 0;

        foreach ($data['settings'] as $key => $value) {
            $this->saveSetting($key, $value);
            $imported_count++;
        }

        return $imported_count;
    }

    /**
     * Получава предложения за автодопълване на blacklist стойности
     */
    public function getBlacklistSuggestions($type, $query) {
        $suggestions = [];

        if ($type === 'ip') {
            // Предложения за IP адреси от customer таблицата
            $sql = "SELECT DISTINCT ip as value, COUNT(*) as count
                    FROM " . DB_PREFIX . "customer
                    WHERE ip LIKE '" . $this->db->escape($query) . "%'
                    GROUP BY ip
                    ORDER BY count DESC
                    LIMIT 10";

            $result = $this->db->query($sql);

            foreach ($result->rows as $row) {
                $suggestions[] = [
                    'value' => $row['value'],
                    'label' => $row['value'] . ' (' . $row['count'] . ' регистрации)',
                    'count' => $row['count']
                ];
            }
        } elseif ($type === 'email') {
            // Предложения за email домейни
            $sql = "SELECT SUBSTRING_INDEX(email, '@', -1) as domain, COUNT(*) as count
                    FROM " . DB_PREFIX . "customer
                    WHERE email LIKE '%@%" . $this->db->escape($query) . "%'
                    GROUP BY domain
                    ORDER BY count DESC
                    LIMIT 10";

            $result = $this->db->query($sql);

            foreach ($result->rows as $row) {
                $suggestions[] = [
                    'value' => $row['domain'],
                    'label' => $row['domain'] . ' (' . $row['count'] . ' потребители)',
                    'count' => $row['count']
                ];
            }
        }

        return $suggestions;
    }

    /**
     * Проверява дали запис съществува в черния списък
     */
    public function checkBlacklistExists($type, $value) {
        $query = $this->db->query("SELECT id FROM " . DB_PREFIX . "fake_blacklist
                                  WHERE type = '" . $this->db->escape($type) . "'
                                  AND value = '" . $this->db->escape($value) . "'");

        return $query->num_rows > 0;
    }
}
