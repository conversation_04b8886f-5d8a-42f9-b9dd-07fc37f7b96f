<?php
class ControllerExtensionModuleFakeDetector extends Controller {
    private $error = [];

    public function index() {
        $this->load->language('extension/module/fake_detector');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('extension/module/fake_detector');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && isset($this->request->post['rescan'])) {
            $this->model_extension_module_fake_detector->runScan();
            $this->session->data['success'] = $this->language->get('text_rescan_success');
            $this->response->redirect($this->url->link('extension/module/fake_detector', 'user_token=' . $this->session->data['user_token'], true));
        }

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_rescan'] = $this->language->get('text_rescan');
        $data['text_description'] = $this->language->get('text_description');
        $data['button_rescan'] = $this->language->get('button_rescan');
        $data['rescan_action'] = $this->url->link('extension/module/fake_detector', 'user_token=' . $data['user_token'], true);
        $data['success'] = $this->session->data['success'] ?? '';
        $this->session->data['success'] = '';

        // Зареждаме списъка с маркирани акаунти
        $results = $this->model_extension_module_fake_detector->getFlaggedCustomers();
        $data['customers'] = $results;
        $data['approve_url'] = $this->url->link('extension/module/fake_detector/approve', 'user_token=' . $data['user_token'], true);
        $data['delete_url'] = $this->url->link('extension/module/fake_detector/delete', 'user_token=' . $data['user_token'], true);

        $this->response->setOutput($this->load->view('extension/module/fake_detector_log', $data));
    }

    public function approve() {
        $this->load->model('extension/module/fake_detector');
        if (isset($this->request->get['customer_id'])) {
            $this->model_extension_module_fake_detector->approveCustomer((int)$this->request->get['customer_id']);
            $this->session->data['success'] = 'Акаунтът е отбелязан като одобрен.';
        }
        $this->response->redirect($this->url->link('extension/module/fake_detector', 'user_token=' . $this->session->data['user_token'], true));
    }

    public function delete() {
        $this->load->model('extension/module/fake_detector');
        if (isset($this->request->get['customer_id'])) {
            $this->model_extension_module_fake_detector->deleteCustomer((int)$this->request->get['customer_id']);
            $this->session->data['success'] = 'Акаунтът е изтрит.';
        }
        $this->response->redirect($this->url->link('extension/module/fake_detector', 'user_token=' . $this->session->data['user_token'], true));
    }
}
