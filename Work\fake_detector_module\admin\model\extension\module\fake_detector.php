<?php
class ModelExtensionModuleFakeDetector extends Model {
    public function runScan() {
        require_once(DIR_SYSTEM . 'fake_detector_rules.php');
        $rules = include DIR_SYSTEM . 'fake_detector_rules.php';
        $blacklist = ['ip' => [], 'email' => []];

        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "fake_blacklist");
        foreach ($query->rows as $row) {
            $blacklist[$row['type']][] = strtolower($row['value']);
        }

        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer");
        foreach ($query->rows as $row) {
            $reasons = [];
            $firstname = $row['firstname'];
            $email = strtolower($row['email']);
            $domain = substr(strrchr($email, "@"), 1);
            $ip = $row['ip'];
            $customer_id = (int)$row['customer_id'];

            if (strlen($firstname) >= $rules['min_username_length'] &&
                preg_match($rules['regex_username'], $firstname)) {
                $reasons[] = "Подозрително име";
            }

            if (in_array($domain, $rules['bad_domains'])) {
                $reasons[] = "Съмнителен домейн ($domain)";
            }

            $ip_count = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "customer WHERE ip = '" . $this->db->escape($ip) . "'")->row['total'];
            if ($ip_count >= $rules['max_accounts_per_ip']) {
                $reasons[] = "IP с много регистрации ($ip_count)";
            }

            if ($rules['only_no_orders']) {
                $order_count = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "order WHERE customer_id = $customer_id")->row['total'];
                if ($order_count == 0) {
                    $reasons[] = "Няма поръчки";
                }
            }

            if ($rules['check_blacklist']) {
                if (in_array($ip, $blacklist['ip'])) {
                    $reasons[] = "IP в черен списък";
                }
                if (in_array($email, $blacklist['email'])) {
                    $reasons[] = "Имейл в черен списък";
                }
            }

            if (!empty($reasons)) {
                $exists = $this->db->query("SELECT * FROM " . DB_PREFIX . "fake_customer_log WHERE customer_id = $customer_id");
                if (!$exists->num_rows) {
                    $this->db->query("INSERT INTO " . DB_PREFIX . "fake_customer_log (customer_id, email, ip, reason, date_detected) VALUES (
                        $customer_id,
                        '" . $this->db->escape($email) . "',
                        '" . $this->db->escape($ip) . "',
                        '" . $this->db->escape(implode('; ', $reasons)) . "',
                        NOW()
                    )");
                }
            }
        }
    }

    public function getFlaggedCustomers() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "fake_customer_log WHERE status = 'pending' ORDER BY date_detected DESC");
        return $query->rows;
    }

    public function approveCustomer($customer_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "fake_customer_log SET status = 'approved' WHERE customer_id = " . (int)$customer_id);
    }

    public function deleteCustomer($customer_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "customer WHERE customer_id = " . (int)$customer_id);
        $this->db->query("DELETE FROM " . DB_PREFIX . "address WHERE customer_id = " . (int)$customer_id);
        $this->db->query("DELETE FROM " . DB_PREFIX . "customer_activity WHERE customer_id = " . (int)$customer_id);
        $this->db->query("DELETE FROM " . DB_PREFIX . "customer_login WHERE customer_id = " . (int)$customer_id);
        $this->db->query("UPDATE " . DB_PREFIX . "fake_customer_log SET status = 'deleted' WHERE customer_id = " . (int)$customer_id);
    }

    public function getBlacklist() {
        $query = $this->db->query("SELECT id AS blacklist_id, type, value, date_added FROM " . DB_PREFIX . "fake_blacklist ORDER BY date_added DESC");
        return $query->rows;
    }

    public function addToBlacklist($type, $value) {
        $this->db->query("INSERT IGNORE INTO " . DB_PREFIX . "fake_blacklist (type, value, date_added) VALUES (
            '" . $this->db->escape($type) . "',
            '" . $this->db->escape(strtolower($value)) . "',
            NOW()
        )");
    }

    public function removeFromBlacklist($blacklist_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "fake_blacklist WHERE id = " . (int)$blacklist_id);
    }

}
