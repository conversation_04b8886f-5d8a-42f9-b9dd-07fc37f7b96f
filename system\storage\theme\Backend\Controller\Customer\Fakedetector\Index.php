<?php

namespace Theme25\Backend\Controller\Customer\Fakedetector;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('fake_detector.log');
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'fake-detector.js',
        ]);
    }

    /**
     * Изпълнява подготовката на данните за основната страница
     */
    public function execute() {
        $this->setTitle('Детектор на фалшиви регистрации');
        $this->initAdminData();
        
        $this->prepareFakeDetectorData()
             ->prepareFilterOptions()
             ->preparePagination()
             ->prepareActionButtons();
        
        $this->renderTemplateWithDataAndOutput('customer/fake_detector');
    }

    /**
     * Подготвя данните за маркираните потребители
     */
    private function prepareFakeDetectorData() {
        $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
        
        // Филтри от заявката
        $filter_data = [
            'filter_status' => $this->requestGet('filter_status', ''),
            'filter_reason' => $this->requestGet('filter_reason', ''),
            'filter_email' => $this->requestGet('filter_email', ''),
            'filter_ip' => $this->requestGet('filter_ip', ''),
            'filter_date_from' => $this->requestGet('filter_date_from', ''),
            'filter_date_to' => $this->requestGet('filter_date_to', ''),
            'sort' => $this->requestGet('sort', 'date_detected'),
            'order' => $this->requestGet('order', 'DESC'),
            'start' => ($this->requestGet('page', 1) - 1) * $this->getConfig('config_limit_admin'),
            'limit' => $this->getConfig('config_limit_admin')
        ];

        // Получаване на маркираните потребители
        $flagged_customers = $this->fakeDetectorModel->getFlaggedCustomers($filter_data);
        $total_flagged = $this->fakeDetectorModel->getTotalFlaggedCustomers($filter_data);

        // Подготовка на данните за изгледа
        $customers = [];
        foreach ($flagged_customers as $customer) {
            $customers[] = [
                'id' => $customer['id'],
                'customer_id' => $customer['customer_id'],
                'email' => $customer['email'],
                'ip' => $customer['ip'],
                'reason' => $customer['reason'],
                'date_detected' => date('d.m.Y H:i', strtotime($customer['date_detected'])),
                'status' => $customer['status'],
                'status_text' => $this->getStatusText($customer['status']),
                'approve_url' => $this->getAdminLink('customer/fakedetector/approve', 'customer_id=' . $customer['customer_id']),
                'delete_url' => $this->getAdminLink('customer/fakedetector/delete', 'customer_id=' . $customer['customer_id']),
                'customer_url' => $this->getAdminLink('customer/customer/edit', 'customer_id=' . $customer['customer_id'])
            ];
        }

        $this->setData([
            'customers' => $customers,
            'total_customers' => $total_flagged,
            'filter_status' => $filter_data['filter_status'],
            'filter_reason' => $filter_data['filter_reason'],
            'filter_email' => $filter_data['filter_email'],
            'filter_ip' => $filter_data['filter_ip'],
            'filter_date_from' => $filter_data['filter_date_from'],
            'filter_date_to' => $filter_data['filter_date_to'],
            'sort' => $filter_data['sort'],
            'order' => $filter_data['order']
        ]);

        return $this;
    }

    /**
     * Подготвя опциите за филтриране
     */
    private function prepareFilterOptions() {
        // Статуси
        $statuses = [
            '' => 'Всички статуси',
            'pending' => 'Чакащи',
            'approved' => 'Одобрени',
            'deleted' => 'Изтрити'
        ];

        // Най-чести причини
        $common_reasons = $this->fakeDetectorModel->getCommonReasons();

        $this->setData([
            'status_options' => $statuses,
            'common_reasons' => $common_reasons
        ]);

        return $this;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $page = $this->requestGet('page', 1);
        $limit = $this->getConfig('config_limit_admin');
        $total = $this->getData('total_customers');

        $pagination = new \Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $limit;
        $pagination->url = $this->getAdminLink('customer/fakedetector', 'page={page}');

        $this->setData([
            'pagination' => $pagination->render(),
            'results' => sprintf(
                'Показани %d до %d от %d (%d страници)',
                ($page - 1) * $limit + 1,
                min($page * $limit, $total),
                $total,
                ceil($total / $limit)
            )
        ]);

        return $this;
    }

    /**
     * Подготвя бутоните за действия
     */
    private function prepareActionButtons() {
        $this->setData([
            'scan_url' => $this->getAdminLink('customer/fakedetector/scan'),
            'manual_scan_url' => $this->getAdminLink('customer/fakedetector/manual'),
            'blacklist_url' => $this->getAdminLink('customer/fakedetector/blacklist'),
            'settings_url' => $this->getAdminLink('customer/fakedetector/settings'),
            'statistics_url' => $this->getAdminLink('customer/fakedetector/statistics'),
            'export_url' => $this->getAdminLink('customer/fakedetector/export'),
            'install_url' => $this->getAdminLink('customer/fakedetector/install'),
            'validate_url' => $this->getAdminLink('customer/fakedetector/validate')
        ]);

        return $this;
    }

    /**
     * Връща текстовото представяне на статуса
     */
    private function getStatusText($status) {
        $statuses = [
            'pending' => 'Чакащ',
            'approved' => 'Одобрен',
            'deleted' => 'Изтрит'
        ];

        return isset($statuses[$status]) ? $statuses[$status] : $status;
    }

    /**
     * AJAX метод за бързо одобряване
     */
    public function quickApprove() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_id = (int)$this->requestPost('customer_id', 0);
            
            if ($customer_id) {
                $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                
                if ($this->fakeDetectorModel->approveCustomer($customer_id)) {
                    $json['success'] = 'Потребителят е одобрен успешно!';
                } else {
                    $json['error'] = 'Грешка при одобряване на потребителя!';
                }
            } else {
                $json['error'] = 'Невалиден ID на потребител!';
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за бързо изтриване
     */
    public function quickDelete() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/fakedetector')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $customer_id = (int)$this->requestPost('customer_id', 0);
            
            if ($customer_id) {
                $this->loadModelAs('customer/fakedetector', 'fakeDetectorModel');
                
                if ($this->fakeDetectorModel->deleteCustomer($customer_id)) {
                    $json['success'] = 'Потребителят е изтрит успешно!';
                } else {
                    $json['error'] = 'Грешка при изтриване на потребителя!';
                }
            } else {
                $json['error'] = 'Невалиден ID на потребител!';
            }
        }

        $this->setJSONResponseOutput($json);
    }
}
