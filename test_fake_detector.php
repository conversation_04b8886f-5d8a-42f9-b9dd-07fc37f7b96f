<?php
/**
 * Fake Detector Module Test Runner
 * Скрипт за изпълнение на тестовете за Fake Detector модула
 * 
 * Използване:
 * php test_fake_detector.php
 */

// Настройка на пътищата
define('DIR_APPLICATION', __DIR__ . '/');
define('DIR_SYSTEM', __DIR__ . '/system/');
define('DIR_LANGUAGE', __DIR__ . '/language/');
define('DIR_TEMPLATE', __DIR__ . '/view/template/');
define('DIR_CONFIG', __DIR__ . '/config/');
define('DIR_IMAGE', __DIR__ . '/image/');
define('DIR_CACHE', __DIR__ . '/system/storage/cache/');
define('DIR_DOWNLOAD', __DIR__ . '/system/storage/download/');
define('DIR_LOGS', __DIR__ . '/system/storage/logs/');
define('DIR_MODIFICATION', __DIR__ . '/system/storage/modification/');
define('DIR_SESSION', __DIR__ . '/system/storage/session/');
define('DIR_UPLOAD', __DIR__ . '/system/storage/upload/');

// Зареждане на конфигурацията
if (file_exists(__DIR__ . '/config.php')) {
    require_once(__DIR__ . '/config.php');
} else {
    // Основни настройки ако няма config.php
    define('DB_DRIVER', 'mysqli');
    define('DB_HOSTNAME', 'localhost');
    define('DB_USERNAME', 'root');
    define('DB_PASSWORD', '');
    define('DB_DATABASE', 'rakla_bg');
    define('DB_PORT', '3306');
    define('DB_PREFIX', 'oc_');
}

// Зареждане на основните класове
require_once(DIR_SYSTEM . 'library/db.php');

// Създаване на връзка с базата данни
try {
    $db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
} catch (Exception $e) {
    die("❌ Грешка при свързване с базата данни: " . $e->getMessage() . "\n");
}

// Създаване на mock config обект
class MockConfig {
    private $data = [];
    
    public function get($key) {
        return isset($this->data[$key]) ? $this->data[$key] : null;
    }
    
    public function set($key, $value) {
        $this->data[$key] = $value;
    }
}

$config = new MockConfig();
$config->set('config_error_log', true);

// Зареждане на тестовия клас
require_once(DIR_SYSTEM . 'storage/theme/Backend/Test/FakedetectorTest.php');

// Изпълнение на тестовете
echo "🚀 Fake Detector Module Test Suite\n";
echo "==================================\n\n";

try {
    $test = new \Theme25\Backend\Test\FakedetectorTest($db, $config);
    $results = $test->runAllTests();
    
    // Показване на финалните резултати
    echo "\n🎯 ФИНАЛНИ РЕЗУЛТАТИ:\n";
    echo "=====================\n";
    echo "Общо тестове: {$results['total_tests']}\n";
    echo "Успешни: {$results['passed_tests']}\n";
    echo "Неуспешни: {$results['failed_tests']}\n";
    echo "Процент успех: {$results['success_rate']}%\n";
    
    if ($results['all_passed']) {
        echo "\n🎉 Всички тестове преминаха успешно!\n";
        echo "Модулът е готов за използване.\n";
        exit(0);
    } else {
        echo "\n⚠️ Има проблеми, които трябва да бъдат решени.\n";
        echo "Моля прегледайте грешките по-горе.\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "❌ Критична грешка при изпълнение на тестовете: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
