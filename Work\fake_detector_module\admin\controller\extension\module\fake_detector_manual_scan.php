<?php
class ControllerExtensionModuleFakeDetectorManualScan extends Controller {
    public function index() {
        $this->load->language('extension/module/fake_detector');
        $this->document->setTitle('Ръчно сканиране');

        $this->load->model('extension/module/fake_detector');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_extension_module_fake_detector->scanAccounts(false); // не-тестов режим
            $this->session->data['success'] = 'Сканирането е извършено успешно!';
            $this->response->redirect($this->url->link('extension/module/fake_detector_manual_scan', 'user_token=' . $this->session->data['user_token'], true));
        }

        $data['user_token'] = $this->session->data['user_token'];
        $data['action'] = $this->url->link('extension/module/fake_detector_manual_scan', 'user_token=' . $this->session->data['user_token'], true);
        $data['success'] = $this->session->data['success'] ?? '';
        $this->session->data['success'] = '';

        $this->response->setOutput($this->load->view('extension/module/fake_detector_manual_scan', $data));
    }

    protected function validate() {
        if (!$this->user->hasPermission('modify', 'extension/module/fake_detector_manual_scan')) {
            $this->session->data['error'] = 'Нямате права за промяна!';
            return false;
        }
        return true;
    }
}
