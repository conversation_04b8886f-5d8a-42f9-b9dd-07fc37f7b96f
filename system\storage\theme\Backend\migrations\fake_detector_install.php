<?php
/**
 * Миграционен скрипт за инсталация на Fake Detector модула
 * 
 * Този скрипт може да се изпълни независимо за създаване на необходимите таблици
 * 
 * Използване:
 * php fake_detector_install.php
 * 
 * Или чрез включване в друг скрипт:
 * require_once 'fake_detector_install.php';
 * $installer = new FakeDetectorInstaller($db);
 * $installer->install();
 */

class FakeDetectorInstaller {
    
    private $db;
    private $db_prefix;
    
    public function __construct($database_connection, $prefix = 'oc_') {
        $this->db = $database_connection;
        $this->db_prefix = $prefix;
    }
    
    /**
     * Инсталира всички таблици за Fake Detector модула
     */
    public function install() {
        echo "Започвам инсталация на Fake Detector модула...\n";
        
        try {
            $this->createCustomerLogTable();
            $this->createBlacklistTable();
            $this->createSettingsTable();
            $this->insertDefaultSettings();
            
            echo "✅ Инсталацията завърши успешно!\n";
            return true;
            
        } catch (Exception $e) {
            echo "❌ Грешка при инсталация: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Създава таблицата за маркирани потребители
     */
    private function createCustomerLogTable() {
        echo "Създавам таблица {$this->db_prefix}fake_customer_log...\n";
        
        $sql = "
            CREATE TABLE IF NOT EXISTS `{$this->db_prefix}fake_customer_log` (
                `id` INT NOT NULL AUTO_INCREMENT,
                `customer_id` INT NOT NULL,
                `email` VARCHAR(255),
                `ip` VARCHAR(45),
                `reason` TEXT,
                `date_detected` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `status` ENUM('pending','approved','deleted') DEFAULT 'pending',
                PRIMARY KEY (`id`),
                KEY `customer_id` (`customer_id`),
                KEY `status` (`status`),
                KEY `date_detected` (`date_detected`),
                KEY `ip` (`ip`),
                KEY `email` (`email`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='Лог на маркирани потребители от Fake Detector модула';
        ";
        
        $this->executeQuery($sql);
    }
    
    /**
     * Създава таблицата за черен списък
     */
    private function createBlacklistTable() {
        echo "Създавам таблица {$this->db_prefix}fake_blacklist...\n";
        
        $sql = "
            CREATE TABLE IF NOT EXISTS `{$this->db_prefix}fake_blacklist` (
                `id` INT NOT NULL AUTO_INCREMENT,
                `type` ENUM('ip', 'email') NOT NULL,
                `value` VARCHAR(255) NOT NULL,
                `note` VARCHAR(255),
                `date_added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `type_value` (`type`, `value`),
                KEY `type` (`type`),
                KEY `date_added` (`date_added`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='Черен списък за IP адреси и email домейни';
        ";
        
        $this->executeQuery($sql);
    }
    
    /**
     * Създава таблицата за настройки
     */
    private function createSettingsTable() {
        echo "Създавам таблица {$this->db_prefix}fake_detector_settings...\n";
        
        $sql = "
            CREATE TABLE IF NOT EXISTS `{$this->db_prefix}fake_detector_settings` (
                `id` INT NOT NULL AUTO_INCREMENT,
                `setting_key` VARCHAR(100) NOT NULL,
                `setting_value` TEXT,
                `date_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `setting_key` (`setting_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='Настройки за Fake Detector модула';
        ";
        
        $this->executeQuery($sql);
    }
    
    /**
     * Добавя начални настройки
     */
    private function insertDefaultSettings() {
        echo "Добавям начални настройки...\n";
        
        $default_settings = [
            'min_username_length' => '10',
            'regex_username' => '/^[a-zA-Z0-9]{10,}$/',
            'bad_domains' => json_encode(['hotmail.com', 'mail.ru', 'yopmail.com', '10minutemail.com', 'guerrillamail.com']),
            'max_accounts_per_ip' => '3',
            'only_no_orders' => '1',
            'check_blacklist' => '1',
            'test_mode' => '1',
            'auto_scan_enabled' => '0',
            'scan_interval_hours' => '24',
            'delete_after_days' => '30'
        ];

        foreach ($default_settings as $key => $value) {
            $sql = "INSERT IGNORE INTO `{$this->db_prefix}fake_detector_settings` 
                    (`setting_key`, `setting_value`) 
                    VALUES ('" . $this->escapeString($key) . "', '" . $this->escapeString($value) . "')";
            $this->executeQuery($sql);
        }
    }
    
    /**
     * Изпълнява SQL заявка
     */
    private function executeQuery($sql) {
        if (!$this->db->query($sql)) {
            throw new Exception("Грешка при изпълнение на заявка: " . $this->db->error);
        }
    }
    
    /**
     * Екранира стринг за SQL заявка
     */
    private function escapeString($string) {
        return $this->db->real_escape_string($string);
    }
    
    /**
     * Проверява дали таблиците съществуват
     */
    public function checkInstallation() {
        $tables = [
            $this->db_prefix . 'fake_customer_log',
            $this->db_prefix . 'fake_blacklist',
            $this->db_prefix . 'fake_detector_settings'
        ];

        $existing = 0;
        foreach ($tables as $table) {
            $result = $this->db->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                $existing++;
            }
        }

        return [
            'total' => count($tables),
            'existing' => $existing,
            'installed' => $existing === count($tables)
        ];
    }
    
    /**
     * Деинсталира модула (премахва таблиците)
     */
    public function uninstall() {
        echo "Започвам деинсталация на Fake Detector модула...\n";
        
        try {
            $this->executeQuery("DROP TABLE IF EXISTS `{$this->db_prefix}fake_customer_log`");
            $this->executeQuery("DROP TABLE IF EXISTS `{$this->db_prefix}fake_blacklist`");
            $this->executeQuery("DROP TABLE IF EXISTS `{$this->db_prefix}fake_detector_settings`");
            
            echo "✅ Деинсталацията завърши успешно!\n";
            return true;
            
        } catch (Exception $e) {
            echo "❌ Грешка при деинсталация: " . $e->getMessage() . "\n";
            return false;
        }
    }
}

// Ако скриптът се изпълнява директно
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    // Тук трябва да се дефинират настройките за база данни
    // Или да се включи config файла на OpenCart
    
    echo "Fake Detector Migration Script\n";
    echo "==============================\n";
    echo "За изпълнение на скрипта, моля включете го в OpenCart среда\n";
    echo "или дефинирайте настройките за база данни.\n";
}
