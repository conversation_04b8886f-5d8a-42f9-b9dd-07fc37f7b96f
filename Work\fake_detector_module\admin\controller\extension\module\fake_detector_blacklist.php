<?php
class ControllerExtensionModuleFakeDetectorBlacklist extends Controller {
    public function index() {
        $this->load->language('extension/module/fake_detector');
        $this->document->setTitle('Черен списък');
        $this->load->model('extension/module/fake_detector');

        // Добавяне в черния списък
        if ($this->request->server['REQUEST_METHOD'] == 'POST' && isset($this->request->post['type'], $this->request->post['value'])) {
            $this->model_extension_module_fake_detector->addToBlacklist($this->request->post['type'], $this->request->post['value']);
            $this->session->data['success'] = 'Добавено успешно!';
            $this->response->redirect($this->url->link('extension/module/fake_detector_blacklist', 'user_token=' . $this->session->data['user_token'], true));
        }

        // Премахване от черния списък
        if (isset($this->request->get['blacklist_id'])) {
            $this->model_extension_module_fake_detector->removeFromBlacklist((int)$this->request->get['blacklist_id']);
            $this->session->data['success'] = 'Записът е премахнат!';
            $this->response->redirect($this->url->link('extension/module/fake_detector_blacklist', 'user_token=' . $this->session->data['user_token'], true));
        }

        // Подготовка на данните за изгледа
        $data['blacklist']  = $this->model_extension_module_fake_detector->getBlacklist();
        $data['add_action'] = $this->url->link('extension/module/fake_detector_blacklist', 'user_token=' . $this->session->data['user_token'], true);
        $data['delete_url'] = $this->url->link('extension/module/fake_detector_blacklist', 'user_token=' . $this->session->data['user_token'], true);
        $data['success']    = $this->session->data['success'] ?? '';
        $this->session->data['success'] = '';

        $data['user_token'] = $this->session->data['user_token'];

        $this->response->setOutput($this->load->view('extension/module/fake_detector_blacklist', $data));
    }
}
