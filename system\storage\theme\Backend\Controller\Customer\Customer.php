<?php

namespace Theme25\Backend\Controller\Customer;

class Customer extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'customer/customer');
    }

    /**
     * Главна страница с клиенти - dispatcher метод
     */
    public function index() {
        $subController = $this->setBackendSubController('Customer/Customer/Index', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Клиенти');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/customer');
        }
    }

    /**
     * Добавяне на нов клиент - dispatcher метод
     */
    public function add() {
        $subController = $this->setBackendSubController('Customer/Customer/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Добавяне на клиент');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/customer_form');
        }
    }

    /**
     * Редактиране на клиент - dispatcher метод
     */
    public function edit() {
        $subController = $this->setBackendSubController('Customer/Customer/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Редактиране на клиент');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/customer_form');
        }
    }

    /**
     * Запазване на клиент - dispatcher метод
     */
    public function save() {
        $subController = $this->setBackendSubController('Customer/Customer/Save', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Изтриване на клиент - dispatcher метод
     */
    public function delete() {
        $subController = $this->setBackendSubController('Customer/Customer/Delete', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * AJAX методи за автокомплийт и други динамични операции
     */
    public function autocomplete() {
        $json = [];
        
        if ($this->requestGet('type')) {
            $type = $this->requestGet('type');
            
            // Динамично зареждане на суб-контролер
            $sub_controller = $this->setBackendSubController('Customer/Customer/' . ucfirst($type) . 'Autocomplete', $this);
            
            if ($sub_controller && is_callable([$sub_controller, 'autocomplete'])) {
                $json = $sub_controller->autocomplete($this->requestGet());
            }
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Логин като клиент
     */
    public function login() {
        $subController = $this->setBackendSubController('Customer/Customer/Login', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->response->redirect($this->getAdminLink('customer/customer'));
        }
    }

    /**
     * Управление на транзакции
     */
    public function transaction() {
        $subController = $this->setBackendSubController('Customer/Customer/Transaction', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Управление на IP адреси
     */
    public function ip() {
        $subController = $this->setBackendSubController('Customer/Customer/Ip', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Управление на поръчки
     */
    public function orders() {
        $subController = $this->setBackendSubController('Customer/Customer/Orders', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Отключване на клиент
     */
    public function unlock() {
        $subController = $this->setBackendSubController('Customer/Customer/Unlock', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Bulk активиране на клиенти - dispatcher метод
     */
    public function activate() {
        $subController = $this->setBackendSubController('Customer/Customer/Activate', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Bulk деактивиране на клиенти - dispatcher метод
     */
    public function deactivate() {
        $subController = $this->setBackendSubController('Customer/Customer/Deactivate', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * История на клиента - dispatcher метод
     */
    public function history() {
        $subController = $this->setBackendSubController('Customer/Customer/History', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            return '';
        }
    }

    /**
     * Добавяне на история - dispatcher метод
     */
    public function addhistory() {
        $subController = $this->setBackendSubController('Customer/Customer/History', $this);
        if ($subController) {
            return $subController->addhistory();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Добавяне на транзакция - dispatcher метод
     */
    public function addtransaction() {
        $subController = $this->setBackendSubController('Customer/Customer/Transaction', $this);
        if ($subController) {
            return $subController->addtransaction();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Reward points на клиента - dispatcher метод
     */
    public function reward() {
        $subController = $this->setBackendSubController('Customer/Customer/Reward', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            return '';
        }
    }

    /**
     * Добавяне на reward points - dispatcher метод
     */
    public function addreward() {
        $subController = $this->setBackendSubController('Customer/Customer/Reward', $this);
        if ($subController) {
            return $subController->addreward();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }
}
