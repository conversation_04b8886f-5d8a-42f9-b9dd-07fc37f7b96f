/**
 * Fake Detector Module JavaScript
 * Следва BackendModule pattern на Rakla.bg проекта
 */

(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initFakeDetector();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        Object.assign(BackendModule, {
            
            // Конфигурация за Fake Detector
            fakeDetector: {
                config: null,
                elements: {},
                isInitialized: false
            },

            // Инициализация на Fake Detector модула
            initFakeDetector: function() {
                if (this.fakeDetector.isInitialized) return;
                
                // Проверка дали сме на Fake Detector страница
                if (!window.fakeDetectorConfig) return;
                
                this.fakeDetector.config = window.fakeDetectorConfig;
                this.cacheFakeDetectorElements();
                this.bindFakeDetectorEvents();
                this.initFakeDetectorComponents();
                
                this.fakeDetector.isInitialized = true;
                this.log('Fake Detector module initialized');
            },

            // Кеширане на DOM елементи
            cacheFakeDetectorElements: function() {
                this.fakeDetector.elements = {
                    // Основни форми и таблици
                    customerForm: document.getElementById('form-customer'),
                    customerTable: document.querySelector('.table-responsive table'),
                    
                    // Филтри
                    filterForm: document.querySelector('.filter-form'),
                    filterStatus: document.getElementById('filter-status'),
                    filterReason: document.getElementById('filter-reason'),
                    filterEmail: document.getElementById('filter-email'),
                    filterIp: document.getElementById('filter-ip'),
                    filterDateFrom: document.getElementById('filter-date-from'),
                    filterDateTo: document.getElementById('filter-date-to'),
                    
                    // Бутони за действия
                    scanButton: document.getElementById('button-scan'),
                    approveSelectedButton: document.getElementById('button-approve-selected'),
                    deleteSelectedButton: document.getElementById('button-delete-selected'),
                    exportButton: document.getElementById('button-export'),
                    
                    // Checkboxes
                    selectAllCheckbox: document.querySelector('input[onclick*="selected"]'),
                    customerCheckboxes: document.querySelectorAll('input[name*="selected"]'),
                    
                    // Модали
                    loadingOverlay: document.getElementById('loading'),
                    
                    // Статистики
                    statsCards: document.querySelectorAll('.stats-card')
                };
            },

            // Свързване на събития
            bindFakeDetectorEvents: function() {
                const self = this;
                
                // Scan бутон - AJAX заявка вместо навигация
                if (self.fakeDetector.elements.scanButton) {
                    self.fakeDetector.elements.scanButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.startFakeDetectorScan();
                    });
                }
                
                // Одобри избраните
                if (self.fakeDetector.elements.approveSelectedButton) {
                    self.fakeDetector.elements.approveSelectedButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.approveSelectedCustomers();
                    });
                }
                
                // Изтрий избраните
                if (self.fakeDetector.elements.deleteSelectedButton) {
                    self.fakeDetector.elements.deleteSelectedButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.deleteSelectedCustomers();
                    });
                }
                
                // Експорт
                if (self.fakeDetector.elements.exportButton) {
                    self.fakeDetector.elements.exportButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.exportFakeDetectorData();
                    });
                }
                
                // Select all checkbox
                if (self.fakeDetector.elements.selectAllCheckbox) {
                    self.fakeDetector.elements.selectAllCheckbox.addEventListener('change', function() {
                        self.toggleAllCustomerSelection(this.checked);
                    });
                }
                
                // Individual checkboxes
                self.fakeDetector.elements.customerCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        self.updateBulkActionButtons();
                    });
                });
                
                // Филтри
                if (self.fakeDetector.elements.filterForm) {
                    const filterInputs = self.fakeDetector.elements.filterForm.querySelectorAll('input, select');
                    filterInputs.forEach(input => {
                        input.addEventListener('change', function() {
                            self.applyFakeDetectorFilters();
                        });
                    });
                }
            },

            // Инициализация на компоненти
            initFakeDetectorComponents: function() {
                this.initFakeDetectorDatePickers();
                this.updateBulkActionButtons();
                this.initFakeDetectorTooltips();
            },

            // Стартиране на сканиране чрез AJAX
            startFakeDetectorScan: function() {
                const self = this;
                
                if (!self.fakeDetector.config.scanUrl) {
                    self.showAlert('error', 'Scan URL не е конфигуриран');
                    return;
                }
                
                // Показване на loading
                self.showFakeDetectorLoading('Стартиране на сканиране...');
                
                fetch(self.fakeDetector.config.scanUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        user_token: self.config.userToken
                    })
                })
                .then(response => response.json())
                .then(data => {
                    self.hideFakeDetectorLoading();
                    
                    if (data.success) {
                        self.showAlert('success', data.message || 'Сканирането започна успешно');
                        
                        // Презареждане на страницата след 2 секунди
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        self.showAlert('error', data.error || 'Възникна грешка при сканирането');
                    }
                })
                .catch(error => {
                    self.hideFakeDetectorLoading();
                    console.error('Scan error:', error);
                    self.showAlert('error', 'Възникна грешка при сканирането');
                });
            },

            // Одобряване на избрани потребители
            approveSelectedCustomers: function() {
                const selectedIds = this.getSelectedCustomerIds();
                
                if (selectedIds.length === 0) {
                    this.showAlert('warning', 'Моля изберете поне един потребител');
                    return;
                }
                
                if (!confirm(`Сигурни ли сте, че искате да одобрите ${selectedIds.length} потребител(и)?`)) {
                    return;
                }
                
                this.performBulkAction('approve', selectedIds);
            },

            // Изтриване на избрани потребители
            deleteSelectedCustomers: function() {
                const selectedIds = this.getSelectedCustomerIds();
                
                if (selectedIds.length === 0) {
                    this.showAlert('warning', 'Моля изберете поне един потребител');
                    return;
                }
                
                if (!confirm(`ВНИМАНИЕ! Сигурни ли сте, че искате да изтриете ${selectedIds.length} потребител(и)? Това действие е необратимо!`)) {
                    return;
                }
                
                this.performBulkAction('delete', selectedIds);
            },

            // Експорт на данни
            exportFakeDetectorData: function() {
                const self = this;
                
                if (!self.fakeDetector.config.exportUrl) {
                    self.showAlert('error', 'Export URL не е конфигуриран');
                    return;
                }
                
                // Създаване на скрит iframe за download
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = self.fakeDetector.config.exportUrl + '&user_token=' + self.config.userToken;
                document.body.appendChild(iframe);
                
                // Премахване на iframe след 5 секунди
                setTimeout(() => {
                    document.body.removeChild(iframe);
                }, 5000);
                
                self.showAlert('info', 'Експортът започна. Файлът ще бъде изтеглен автоматично.');
            },

            // Получаване на избрани ID-та
            getSelectedCustomerIds: function() {
                const selectedCheckboxes = document.querySelectorAll('input[name*="selected"]:checked');
                return Array.from(selectedCheckboxes).map(cb => cb.value);
            },

            // Изпълнение на масово действие
            performBulkAction: function(action, ids) {
                const self = this;
                const config = self.fakeDetector.config;
                
                let url;
                switch (action) {
                    case 'approve':
                        url = config.approveUrl;
                        break;
                    case 'delete':
                        url = config.deleteUrl;
                        break;
                    default:
                        self.showAlert('error', 'Неизвестно действие');
                        return;
                }
                
                if (!url) {
                    self.showAlert('error', 'URL за действието не е конфигуриран');
                    return;
                }
                
                self.showFakeDetectorLoading(`${action === 'approve' ? 'Одобряване' : 'Изтриване'} на потребители...`);
                
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        selected: ids,
                        user_token: self.config.userToken
                    })
                })
                .then(response => response.json())
                .then(data => {
                    self.hideFakeDetectorLoading();
                    
                    if (data.success) {
                        self.showAlert('success', data.message || 'Действието е изпълнено успешно');
                        
                        // Презареждане на страницата
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        self.showAlert('error', data.error || 'Възникна грешка при изпълнението');
                    }
                })
                .catch(error => {
                    self.hideFakeDetectorLoading();
                    console.error('Bulk action error:', error);
                    self.showAlert('error', 'Възникна грешка при изпълнението');
                });
            },

            // Toggle на всички checkboxes
            toggleAllCustomerSelection: function(checked) {
                this.fakeDetector.elements.customerCheckboxes.forEach(checkbox => {
                    checkbox.checked = checked;
                });
                this.updateBulkActionButtons();
            },

            // Обновяване на състоянието на bulk action бутоните
            updateBulkActionButtons: function() {
                const selectedCount = this.getSelectedCustomerIds().length;
                const hasSelection = selectedCount > 0;

                if (this.fakeDetector.elements.approveSelectedButton) {
                    this.fakeDetector.elements.approveSelectedButton.disabled = !hasSelection;
                    this.fakeDetector.elements.approveSelectedButton.textContent =
                        hasSelection ? `Одобри избраните (${selectedCount})` : 'Одобри избраните';
                }

                if (this.fakeDetector.elements.deleteSelectedButton) {
                    this.fakeDetector.elements.deleteSelectedButton.disabled = !hasSelection;
                    this.fakeDetector.elements.deleteSelectedButton.textContent =
                        hasSelection ? `Изтрий избраните (${selectedCount})` : 'Изтрий избраните';
                }
            },

            // Прилагане на филтри
            applyFakeDetectorFilters: function() {
                const form = this.fakeDetector.elements.filterForm;
                if (!form) return;

                const formData = new FormData(form);
                const params = new URLSearchParams();

                for (let [key, value] of formData.entries()) {
                    if (value.trim()) {
                        params.append(key, value);
                    }
                }

                // Добавяне на user_token
                params.append('user_token', this.config.userToken);

                // Навигация с филтрите
                window.location.href = window.location.pathname + '?' + params.toString();
            },

            // Инициализация на date pickers
            initFakeDetectorDatePickers: function() {
                const dateInputs = document.querySelectorAll('#filter-date-from, #filter-date-to');
                dateInputs.forEach(input => {
                    if (input) {
                        input.type = 'date';
                    }
                });
            },

            // Инициализация на tooltips
            initFakeDetectorTooltips: function() {
                const tooltipElements = document.querySelectorAll('[title]');
                tooltipElements.forEach(element => {
                    element.addEventListener('mouseenter', function() {
                        this.setAttribute('data-tooltip', this.getAttribute('title'));
                        this.removeAttribute('title');
                    });
                });
            },

            // Показване на loading overlay
            showFakeDetectorLoading: function(message = 'Зареждане...') {
                const overlay = this.fakeDetector.elements.loadingOverlay;
                if (overlay) {
                    const messageElement = overlay.querySelector('span');
                    if (messageElement) {
                        messageElement.textContent = message;
                    }
                    overlay.classList.remove('hidden');
                }
            },

            // Скриване на loading overlay
            hideFakeDetectorLoading: function() {
                const overlay = this.fakeDetector.elements.loadingOverlay;
                if (overlay) {
                    overlay.classList.add('hidden');
                }
            },

            /**
             * Debug логване
             */
            log: function(message, data) {
                if (window.console && window.console.log) {
                    console.log('[FakeDetectorModule] ' + message, data || '');
                }
            }

        });
    }

})();
