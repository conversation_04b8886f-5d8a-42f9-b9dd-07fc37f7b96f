/**
 * Fake Detector Module JavaScript
 * Следва BackendModule pattern на Rakla.bg проекта
 */

(function() {
    'use strict';

    // Основен модул за Fake Detector
    const FakeDetectorModule = {
        
        // Конфигурация
        config: {
            urls: {
                approve: '',
                delete: '',
                scan: '',
                blacklist: '',
                export: '',
                validate: ''
            },
            selectors: {
                customerTable: '#form-customer',
                selectedCheckboxes: 'input[name*="selected"]:checked',
                filterForm: '.well',
                actionButtons: '.btn-group'
            },
            debug: true
        },

        // Инициализация
        init: function() {
            this.bindEvents();
            this.initDatePickers();
            this.initTooltips();
            this.setupAjaxDefaults();
            this.log('FakeDetector module initialized');
        },

        // Свързване на събития
        bindEvents: function() {
            const self = this;

            // Филтриране
            $(document).on('click', '#button-filter', function(e) {
                e.preventDefault();
                self.applyFilters();
            });

            // Изчистване на филтри
            $(document).on('click', '#button-clear', function(e) {
                e.preventDefault();
                self.clearFilters();
            });

            // Масово одобряване
            $(document).on('click', '#button-approve-selected', function(e) {
                e.preventDefault();
                self.bulkApprove();
            });

            // Масово изтриване
            $(document).on('click', '#button-delete-selected', function(e) {
                e.preventDefault();
                self.bulkDelete();
            });

            // Бързо одобряване
            $(document).on('click', '.btn-approve-quick', function(e) {
                e.preventDefault();
                const customerId = $(this).data('customer-id');
                self.quickApprove(customerId);
            });

            // Бързо изтриване
            $(document).on('click', '.btn-delete-quick', function(e) {
                e.preventDefault();
                const customerId = $(this).data('customer-id');
                self.quickDelete(customerId);
            });

            // Автоматично сканиране
            $(document).on('click', '#button-scan', function(e) {
                e.preventDefault();
                self.runScan();
            });

            // Валидация на модула
            $(document).on('click', '#button-validate', function(e) {
                e.preventDefault();
                self.validateModule();
            });
        },

        // Инициализация на date pickers
        initDatePickers: function() {
            $('.input-group.date input').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true,
                language: 'bg'
            });
        },

        // Инициализация на tooltips
        initTooltips: function() {
            $('[data-toggle="tooltip"]').tooltip();
        },

        // Настройка на AJAX по подразбиране
        setupAjaxDefaults: function() {
            $.ajaxSetup({
                beforeSend: function() {
                    $('#loading').show();
                },
                complete: function() {
                    $('#loading').hide();
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    FakeDetectorModule.showAlert('Възникна грешка при заявката!', 'danger');
                }
            });
        },

        // Прилагане на филтри
        applyFilters: function() {
            const filters = this.getFilterValues();
            const url = this.buildFilterUrl(filters);
            window.location.href = url;
        },

        // Получаване на стойностите на филтрите
        getFilterValues: function() {
            return {
                filter_status: $('select[name="filter_status"]').val(),
                filter_email: $('input[name="filter_email"]').val(),
                filter_ip: $('input[name="filter_ip"]').val(),
                filter_reason: $('input[name="filter_reason"]').val(),
                filter_date_from: $('input[name="filter_date_from"]').val(),
                filter_date_to: $('input[name="filter_date_to"]').val()
            };
        },

        // Изграждане на URL с филтри
        buildFilterUrl: function(filters) {
            const params = new URLSearchParams();
            
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    params.append(key, filters[key]);
                }
            });

            return window.location.pathname + '?' + params.toString();
        },

        // Изчистване на филтри
        clearFilters: function() {
            $('select[name="filter_status"]').val('');
            $('input[name="filter_email"]').val('');
            $('input[name="filter_ip"]').val('');
            $('input[name="filter_reason"]').val('');
            $('input[name="filter_date_from"]').val('');
            $('input[name="filter_date_to"]').val('');
            
            // Пренасочване без параметри
            window.location.href = window.location.pathname;
        },

        // Получаване на избраните потребители
        getSelectedCustomers: function() {
            const selected = [];
            $(this.config.selectors.selectedCheckboxes).each(function() {
                selected.push($(this).val());
            });
            return selected;
        },

        // Масово одобряване
        bulkApprove: function() {
            const selected = this.getSelectedCustomers();
            
            if (selected.length === 0) {
                this.showAlert('Моля изберете потребители за одобряване!', 'warning');
                return;
            }

            if (!confirm(`Сигурни ли сте, че искате да одобрите ${selected.length} потребители?`)) {
                return;
            }

            this.sendAjaxRequest('customer/fakedetector/approve/bulk', {
                customer_ids: selected
            }, (response) => {
                if (response.success) {
                    this.showAlert(response.success, 'success');
                    setTimeout(() => window.location.reload(), 1500);
                }
            });
        },

        // Масово изтриване
        bulkDelete: function() {
            const selected = this.getSelectedCustomers();
            
            if (selected.length === 0) {
                this.showAlert('Моля изберете потребители за изтриване!', 'warning');
                return;
            }

            if (!confirm(`ВНИМАНИЕ! Сигурни ли сте, че искате да изтриете ${selected.length} потребители? Това действие е необратимо!`)) {
                return;
            }

            this.sendAjaxRequest('customer/fakedetector/delete/bulk', {
                customer_ids: selected
            }, (response) => {
                if (response.success) {
                    this.showAlert(response.success, 'success');
                    setTimeout(() => window.location.reload(), 1500);
                }
            });
        },

        // Бързо одобряване
        quickApprove: function(customerId) {
            if (!confirm('Сигурни ли сте, че искате да одобрите този потребител?')) {
                return;
            }

            this.sendAjaxRequest('customer/fakedetector/approve', {
                customer_id: customerId
            }, (response) => {
                if (response.success) {
                    this.showAlert(response.success, 'success');
                    setTimeout(() => window.location.reload(), 1000);
                }
            });
        },

        // Бързо изтриване
        quickDelete: function(customerId) {
            if (!confirm('ВНИМАНИЕ! Сигурни ли сте, че искате да изтриете този потребител? Това действие е необратимо!')) {
                return;
            }

            this.sendAjaxRequest('customer/fakedetector/delete', {
                customer_id: customerId
            }, (response) => {
                if (response.success) {
                    this.showAlert(response.success, 'success');
                    setTimeout(() => window.location.reload(), 1000);
                }
            });
        },

        // Изпълнение на сканиране
        runScan: function() {
            if (!confirm('Сигурни ли сте, че искате да стартирате автоматично сканиране?')) {
                return;
            }

            this.showProgressModal('Изпълнение на сканиране...');

            this.sendAjaxRequest('customer/fakedetector/scan', {}, (response) => {
                this.hideProgressModal();
                
                if (response.success) {
                    this.showAlert(response.success, 'success');
                    if (response.details) {
                        this.showScanResults(response.details);
                    }
                    setTimeout(() => window.location.reload(), 3000);
                }
            });
        },

        // Валидация на модула
        validateModule: function() {
            this.sendAjaxRequest('customer/fakedetector/validate', {}, (response) => {
                if (response.validation) {
                    this.showValidationResults(response.validation);
                }
            });
        },

        // Показване на резултати от сканиране
        showScanResults: function(details) {
            const modal = this.createModal('Резултати от сканиране', `
                <div class="row">
                    <div class="col-sm-6">
                        <strong>Обработени потребители:</strong> ${details.processed_count || 0}
                    </div>
                    <div class="col-sm-6">
                        <strong>Маркирани потребители:</strong> ${details.flagged_count || 0}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-sm-12">
                        <strong>Време на изпълнение:</strong> ${details.timestamp || 'Неизвестно'}
                    </div>
                </div>
            `);
            modal.modal('show');
        },

        // Показване на резултати от валидация
        showValidationResults: function(validation) {
            let content = '<div class="validation-results">';
            
            Object.keys(validation).forEach(key => {
                const result = validation[key];
                const statusClass = result.status === 'OK' ? 'success' : 
                                  result.status === 'WARNING' ? 'warning' : 'danger';
                
                content += `
                    <div class="alert alert-${statusClass}">
                        <strong>${key}:</strong> ${result.message}
                    </div>
                `;
            });
            
            content += '</div>';
            
            const modal = this.createModal('Резултати от валидация', content);
            modal.modal('show');
        },

        // Общ AJAX метод
        sendAjaxRequest: function(url, data, successCallback, errorCallback) {
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                dataType: 'json',
                success: (response) => {
                    if (response.error) {
                        this.showAlert(response.error, 'danger');
                        if (errorCallback) errorCallback(response);
                    } else {
                        if (successCallback) successCallback(response);
                    }
                },
                error: (xhr, status, error) => {
                    this.log('AJAX Error:', error);
                    this.showAlert('Възникна грешка при заявката!', 'danger');
                    if (errorCallback) errorCallback({error: error});
                }
            });
        },

        // Показване на alert съобщения
        showAlert: function(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const iconClass = type === 'success' ? 'fa-check-circle' : 
                            type === 'danger' ? 'fa-exclamation-circle' : 
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

            const alert = $(`
                <div class="alert ${alertClass} alert-dismissible fade in">
                    <i class="fa ${iconClass}"></i> ${message}
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                </div>
            `);

            // Добавяне в началото на container-fluid
            $('.container-fluid').first().prepend(alert);

            // Автоматично скриване след 5 секунди
            setTimeout(() => {
                alert.fadeOut();
            }, 5000);
        },

        // Показване на прогрес модал
        showProgressModal: function(message) {
            const modal = this.createModal('Моля изчакайте...', `
                <div class="text-center">
                    <i class="fa fa-spinner fa-spin fa-3x"></i>
                    <p style="margin-top: 15px;">${message}</p>
                </div>
            `);
            modal.modal({backdrop: 'static', keyboard: false});
        },

        // Скриване на прогрес модал
        hideProgressModal: function() {
            $('.modal').modal('hide');
        },

        // Създаване на модал
        createModal: function(title, content) {
            const modalId = 'fake-detector-modal-' + Date.now();
            const modal = $(`
                <div class="modal fade" id="${modalId}" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                <h4 class="modal-title">${title}</h4>
                            </div>
                            <div class="modal-body">${content}</div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">Затвори</button>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            $('body').append(modal);

            // Премахване на модала след затваряне
            modal.on('hidden.bs.modal', function() {
                $(this).remove();
            });

            return modal;
        },

        // Логване (само в debug режим)
        log: function(...args) {
            if (this.config.debug && console && console.log) {
                console.log('[FakeDetector]', ...args);
            }
        }
    };

    // Инициализация при зареждане на DOM
    $(document).ready(function() {
        FakeDetectorModule.init();
    });

    // Експортиране на модула за глобален достъп
    window.FakeDetectorModule = FakeDetectorModule;

})();
