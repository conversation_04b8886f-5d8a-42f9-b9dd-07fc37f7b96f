[20-Jul-2025 16:59:28 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php:113
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php(63): Theme25\Backend\Controller\Customer\Fakedetector\Scan->prepareManualScanData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php(52): Theme25\Backend\Controller\Customer\Fakedetector\Scan->showManualScanForm()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Scan->manual()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(218): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('Theme25/Backend...', 'manual', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/modification in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Scan.php on line 113
[20-Jul-2025 17:51:42 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function getSettings() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php:124
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(40): Theme25\Backend\Controller\Customer\Fakedetector\Settings->prepareSettingsData()
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php(16): Theme25\Backend\Controller\Customer\Fakedetector\Settings->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Fakedetector\Settings->index()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(219): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'index', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#6 /home/<USER>/storage_theme25/mod in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Fakedetector/Settings.php on line 124
