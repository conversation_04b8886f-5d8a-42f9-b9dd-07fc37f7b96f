# 🛡️ Fake Detector Module v2.0.0

Модул за откриване и управление на фалшиви регистрации, адаптиран към архитектурата на Rakla.bg проекта.

## 📋 Съдържание

- [Описание](#описание)
- [Функционалности](#функционалности)
- [Архитектура](#архитектура)
- [Инсталация](#инсталация)
- [Конфигурация](#конфигурация)
- [Използване](#използване)
- [Тестване](#тестване)
- [Поддръжка](#поддръжка)

## 📖 Описание

Fake Detector модулът е мощен инструмент за автоматично и ръчно откриване на подозрителни регистрации в Rakla.bg платформата. Модулът използва множество критерии за анализ и предоставя удобен интерфейс за управление на маркираните потребители.

## ✨ Функционалности

### 🔍 Откриване на фалшиви регистрации
- **Автоматично сканиране** - периодично сканиране на нови регистрации
- **Ръчно сканиране** - сканиране по заявка с персонализирани настройки
- **Множество критерии** - подозрителни имена, съмнителни домейни, множество IP регистрации
- **Черен списък** - управление на забранени IP адреси и email домейни

### 👥 Управление на потребители
- **Одобряване** - одобряване на маркирани потребители
- **Изтриване** - безопасно изтриване на фалшиви акаунти
- **Масови операции** - работа с множество потребители едновременно
- **Детайлни логове** - проследяване на всички действия

### 📊 Отчети и статистики
- **Детайлни отчети** - статистики за маркирани потребители
- **Филтриране** - търсене по различни критерии
- **Експорт** - експорт на данни в различни формати
- **Визуализация** - графично представяне на данните

## 🏗️ Архитектура

Модулът следва архитектурата на Rakla.bg проекта с Theme25 namespace конвенции:

### 📁 Структура на файловете

```
system/storage/theme/Backend/
├── Controller/Customer/
│   ├── Fakedetector.php                    # Главен dispatcher контролер
│   └── Fakedetector/
│       ├── Index.php                       # Листване на маркирани
│       ├── Scan.php                        # Сканиране
│       ├── Blacklist.php                   # Черен списък
│       ├── Approve.php                     # Одобряване
│       ├── Delete.php                      # Изтриване
│       ├── Install.php                     # Инсталация
│       └── Validate.php                    # Валидация
├── Model/Customer/
│   └── Fakedetector.php                    # Основен модел
├── View/Template/customer/
│   ├── fake_detector.twig                  # Основен изглед
│   ├── fake_detector_blacklist.twig        # Черен списък
│   └── fake_detector_manual_scan.twig      # Ръчно сканиране
├── View/Javascript/
│   ├── fake-detector.js                    # Основен JS
│   ├── fake-detector-blacklist.js          # JS за черен списък
│   └── fake-detector-scan.js               # JS за сканиране
├── Helper/
│   └── Fakedetectorhelper.php              # Helper клас
├── Config/
│   └── fakedetector_config.php             # Конфигурация
├── Test/
│   └── FakedetectorTest.php                # Тестове
└── Controller/Startup/
    └── Fakedetector.php                    # Startup контролер
```

### 🗄️ База данни

Модулът използва 3 основни таблици:

1. **`{prefix}fake_customer_log`** - лог на маркирани потребители
2. **`{prefix}fake_blacklist`** - черен списък за IP/Email
3. **`{prefix}fake_detector_settings`** - настройки на модула

## 🚀 Инсталация

### 1. Копиране на файловете

Всички файлове са вече създадени в правилната структура на Rakla.bg проекта.

### 2. Създаване на таблиците

Изпълнете инсталационния скрипт:

```php
// Чрез админ интерфейса
customer/fakedetector/install

// Или чрез миграционния скрипт
php system/storage/theme/Backend/migrations/fake_detector_install.php
```

### 3. Конфигуриране на правата

Добавете права за достъп в админ панела:
- `customer/fakedetector` - достъп
- `customer/fakedetector` - модификация

## ⚙️ Конфигурация

### Основни настройки

Настройките се управляват чрез админ интерфейса или директно в базата данни:

```php
// Критерии за детекция
'min_username_length' => 10,
'regex_username' => '/^[a-zA-Z0-9]{10,}$/',
'max_accounts_per_ip' => 3,
'only_no_orders' => true,
'check_blacklist' => true,

// Режими на работа
'test_mode' => true,
'auto_scan_enabled' => false,
'scan_interval_hours' => 24,
'delete_after_days' => 30
```

### Съмнителни домейни

Модулът включва обширен списък от временни email услуги и съмнителни домейни.

## 📖 Използване

### 🔍 Основно сканиране

1. Отидете на `customer/fakedetector`
2. Кликнете "Сканирай" за автоматично сканиране
3. Прегледайте резултатите и вземете решения

### 🎯 Ръчно сканиране

1. Отидете на `customer/fakedetector/manual`
2. Изберете критерии и период
3. Стартирайте тестово или реално сканиране
4. Прегледайте резултатите

### 🚫 Управление на черен списък

1. Отидете на `customer/fakedetector/blacklist`
2. Добавете IP адреси или email домейни
3. Управлявайте съществуващите записи

### ✅ Одобряване и изтриване

- **Одобряване** - маркира потребителя като легитимен
- **Изтриване** - изтрива потребителя и всички свързани данни
- **Масови операции** - работа с множество потребители

## 🧪 Тестване

### Изпълнение на тестовете

```bash
php test_fake_detector.php
```

Тестовете проверяват:
- ✅ Архитектурна съвместимост
- ✅ Структура на базата данни
- ✅ Функционалност на контролерите
- ✅ JavaScript интеграция
- ✅ Template структура
- ✅ Конфигурация

### Валидация на модула

```php
// Чрез админ интерфейса
customer/fakedetector/validate
```

## 🔧 Поддръжка

### Логове

Модулът създава детайлни логове в:
- `system/storage/logs/fake_detector.log`
- `system/storage/logs/fake_detector_scan.log`
- `system/storage/logs/fake_detector_blacklist.log`

### Почистване

Автоматично почистване на стари записи според настройката `delete_after_days`.

### Обновления

Модулът поддържа автоматично откриване и прилагане на обновления чрез startup контролера.

## 🔒 Сигурност

- **Права за достъп** - контролиран достъп чрез OpenCart права система
- **SQL Injection защита** - всички заявки използват escape функции
- **CSRF защита** - защита срещу cross-site request forgery
- **Логване** - детайлно логване на всички действия

## 📞 Поддръжка

За въпроси и проблеми:
- Проверете логовете за грешки
- Изпълнете тестовете за диагностика
- Използвайте валидационния модул

## 📄 Лиценз

Proprietary - Rakla.bg Team

---

**Версия:** 2.0.0  
**Съвместимост:** Rakla.bg Theme25  
**Последно обновление:** 2025-07-20
