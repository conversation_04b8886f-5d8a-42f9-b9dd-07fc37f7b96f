<?php

namespace Theme25\Backend\Controller\Customer;

class Fakedetector extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'customer/fakedetector');

        // Автоматична проверка и създаване на таблици при първо зареждане
        $this->ensureTablesExist();
    }

    /**
     * Проверява дали таблиците съществуват и ги създава при нужда
     */
    private function ensureTablesExist() {
        try {
            $installController = new \Theme25\Backend\Controller\Customer\Fakedetector\Install($this);
            $created = $installController->createTablesIfNeeded();

            if ($created) {
                // Логваме успешното създаване на таблиците
                error_log('Fake Detector: Таблиците са създадени автоматично');
            }
        } catch (Exception $e) {
            // Логваме грешката, но не спираме изпълнението
            error_log('Fake Detector: Грешка при проверка на таблици - ' . $e->getMessage());
        }
    }

    /**
     * Главна страница с маркирани потребители - dispatcher метод
     */
    public function index() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Index', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Детектор на фалшиви регистрации');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/fake_detector');
        }
    }

    /**
     * Сканиране за фалшиви регистрации - dispatcher метод
     */
    public function scan() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Scan', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Ръчно сканиране - dispatcher метод
     */
    public function manual() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Scan', $this);
        if ($subController) {
            return $subController->manual();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Управление на черен списък - dispatcher метод
     */
    public function blacklist() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Blacklist', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Черен списък');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/fake_detector_blacklist');
        }
    }

    /**
     * Одобряване на маркиран потребител - dispatcher метод
     */
    public function approve() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Approve', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Изтриване на маркиран потребител - dispatcher метод
     */
    public function delete() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Delete', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Настройки на модула - dispatcher метод
     */
    public function settings() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Settings', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Настройки на детектора');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/fake_detector_settings');
        }
    }

    /**
     * Статистики и отчети - dispatcher метод
     */
    public function statistics() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Statistics', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Статистики');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/fake_detector_statistics');
        }
    }

    /**
     * Инсталация на модула - dispatcher метод
     */
    public function install() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Install', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Валидация на модула - dispatcher метод
     */
    public function validate() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Validate', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * AJAX автодопълване - dispatcher метод
     */
    public function autocomplete() {
        $json = [];
        
        if ($this->requestGet('type')) {
            $type = $this->requestGet('type');
            
            // Динамично зареждане на суб-контролер за автодопълване
            $sub_controller = $this->setBackendSubController('Customer/Fakedetector/' . ucfirst($type) . 'Autocomplete', $this);
            
            if ($sub_controller && is_callable([$sub_controller, 'autocomplete'])) {
                $json = $sub_controller->autocomplete($this->requestGet());
            } else {
                $json['error'] = 'Autocomplete type not supported: ' . $type;
            }
        } else {
            $json['error'] = 'Missing autocomplete type parameter';
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Експорт на данни - dispatcher метод
     */
    public function export() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Export', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Импорт на данни - dispatcher метод
     */
    public function import() {
        $subController = $this->setBackendSubController('Customer/Fakedetector/Import', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

}
