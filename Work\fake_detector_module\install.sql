-- Таблица за маркирани потребители
CREATE TABLE IF NOT EXISTS `oc_fake_customer_log` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `customer_id` INT NOT NULL,
  `email` VARCHAR(255),
  `ip` VARCHAR(45),
  `reason` TEXT,
  `date_detected` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` ENUM('pending','approved','deleted') DEFAULT 'pending',
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Таблица за черен списък (IP или Email)
CREATE TABLE IF NOT EXISTS `oc_fake_blacklist` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` ENUM('ip', 'email') NOT NULL,
  `value` VARCHAR(255) NOT NULL,
  `note` VARCHAR(255),
  `date_added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_value` (`type`, `value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
