<?php

namespace Theme25\Backend\Test;

/**
 * Fake Detector Module Test Suite
 * Тестове за валидация на интеграцията с Rakla.bg архитектурата
 */
class FakedetectorTest {

    private $db;
    private $config;
    private $helper;
    private $test_results = [];
    private $errors = [];

    public function __construct($db, $config) {
        $this->db = $db;
        $this->config = $config;
        $this->initHelper();
    }

    /**
     * Инициализира helper класа
     */
    private function initHelper() {
        require_once DIR_SYSTEM . 'storage/theme/Backend/Helper/Fakedetectorhelper.php';
        $this->helper = new \Theme25\Backend\Helper\Fakedetectorhelper($this->db, $this->config);
    }

    /**
     * Изпълнява всички тестове
     */
    public function runAllTests() {
        $this->test_results = [];
        $this->errors = [];

        echo "🧪 Започвам тестване на Fake Detector модула...\n\n";

        // Тестове за архитектура
        $this->testArchitecture();

        // Тестове за база данни
        $this->testDatabase();

        // Тестове за контролери
        $this->testControllers();

        // Тестове за модели
        $this->testModels();

        // Тестове за helper класове
        $this->testHelpers();

        // Тестове за JavaScript
        $this->testJavaScript();

        // Тестове за templates
        $this->testTemplates();

        // Тестове за конфигурация
        $this->testConfiguration();

        // Тестове за функционалност
        $this->testFunctionality();

        // Показване на резултатите
        $this->displayResults();

        return $this->getTestSummary();
    }

    /**
     * Тестове за архитектурата
     */
    private function testArchitecture() {
        echo "📁 Тестване на архитектурата...\n";

        // Проверка на директории
        $this->testDirectoryStructure();

        // Проверка на namespace конвенции
        $this->testNamespaceConventions();

        // Проверка на файлови конвенции
        $this->testFileConventions();
    }

    /**
     * Тестване на структурата на директориите
     */
    private function testDirectoryStructure() {
        $required_dirs = [
            'system/storage/theme/Backend/Controller/Customer/Fakedetector/',
            'system/storage/theme/Backend/Model/Customer/',
            'system/storage/theme/Backend/View/Template/customer/',
            'system/storage/theme/Backend/View/Javascript/',
            'system/storage/theme/Backend/Helper/',
            'system/storage/theme/Backend/Config/'
        ];

        foreach ($required_dirs as $dir) {
            $full_path = DIR_SYSTEM . '../' . $dir;
            if (is_dir($full_path)) {
                $this->addTestResult('Directory Structure', "✅ {$dir}", true);
            } else {
                $this->addTestResult('Directory Structure', "❌ {$dir} - не съществува", false);
            }
        }
    }

    /**
     * Тестване на namespace конвенциите
     */
    private function testNamespaceConventions() {
        $files_to_check = [
            'system/storage/theme/Backend/Controller/Customer/Fakedetector.php' => 'Theme25\\Backend\\Controller\\Customer\\Fakedetector',
            'system/storage/theme/Backend/Model/Customer/Fakedetector.php' => 'Theme25\\Backend\\Model\\Customer\\Fakedetector',
            'system/storage/theme/Backend/Helper/Fakedetectorhelper.php' => 'Theme25\\Backend\\Helper\\Fakedetectorhelper'
        ];

        foreach ($files_to_check as $file => $expected_namespace) {
            $full_path = DIR_SYSTEM . '../' . $file;
            if (file_exists($full_path)) {
                $content = file_get_contents($full_path);
                if (strpos($content, "namespace {$expected_namespace}") !== false) {
                    $this->addTestResult('Namespace Conventions', "✅ {$file}", true);
                } else {
                    $this->addTestResult('Namespace Conventions', "❌ {$file} - грешен namespace", false);
                }
            } else {
                $this->addTestResult('Namespace Conventions', "❌ {$file} - файлът не съществува", false);
            }
        }
    }

    /**
     * Тестване на файловите конвенции
     */
    private function testFileConventions() {
        $sub_controllers = [
            'Index.php', 'Scan.php', 'Blacklist.php', 'Approve.php', 'Delete.php'
        ];

        foreach ($sub_controllers as $controller) {
            $file_path = DIR_SYSTEM . '../system/storage/theme/Backend/Controller/Customer/Fakedetector/' . $controller;
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                if (strpos($content, 'extends \\Theme25\\ControllerSubMethods') !== false) {
                    $this->addTestResult('File Conventions', "✅ {$controller} - правилно наследяване", true);
                } else {
                    $this->addTestResult('File Conventions', "❌ {$controller} - грешно наследяване", false);
                }
            } else {
                $this->addTestResult('File Conventions', "❌ {$controller} - файлът не съществува", false);
            }
        }
    }

    /**
     * Тестове за базата данни
     */
    private function testDatabase() {
        echo "🗄️ Тестване на базата данни...\n";

        // Проверка на таблици
        $this->testDatabaseTables();

        // Проверка на структурата
        $this->testDatabaseStructure();

        // Проверка на индекси
        $this->testDatabaseIndexes();
    }

    /**
     * Тестване на таблиците в базата данни
     */
    private function testDatabaseTables() {
        $required_tables = [
            'fake_customer_log',
            'fake_blacklist',
            'fake_detector_settings'
        ];

        foreach ($required_tables as $table) {
            $full_table_name = DB_PREFIX . $table;
            $result = $this->db->query("SHOW TABLES LIKE '{$full_table_name}'");
            
            if ($result->num_rows > 0) {
                $this->addTestResult('Database Tables', "✅ {$full_table_name}", true);
            } else {
                $this->addTestResult('Database Tables', "❌ {$full_table_name} - не съществува", false);
            }
        }
    }

    /**
     * Тестване на структурата на базата данни
     */
    private function testDatabaseStructure() {
        // Проверка на колоните в fake_customer_log
        $this->testTableStructure('fake_customer_log', [
            'id', 'customer_id', 'email', 'ip', 'reason', 'date_detected', 'status'
        ]);

        // Проверка на колоните в fake_blacklist
        $this->testTableStructure('fake_blacklist', [
            'id', 'type', 'value', 'note', 'date_added'
        ]);

        // Проверка на колоните в fake_detector_settings
        $this->testTableStructure('fake_detector_settings', [
            'id', 'setting_key', 'setting_value', 'date_modified'
        ]);
    }

    /**
     * Тестване на структурата на конкретна таблица
     */
    private function testTableStructure($table, $required_columns) {
        $full_table_name = DB_PREFIX . $table;
        
        try {
            $result = $this->db->query("DESCRIBE {$full_table_name}");
            $existing_columns = array_column($result->rows, 'Field');
            
            foreach ($required_columns as $column) {
                if (in_array($column, $existing_columns)) {
                    $this->addTestResult('Database Structure', "✅ {$table}.{$column}", true);
                } else {
                    $this->addTestResult('Database Structure', "❌ {$table}.{$column} - липсва", false);
                }
            }
        } catch (Exception $e) {
            $this->addTestResult('Database Structure', "❌ {$table} - грешка при проверка", false);
        }
    }

    /**
     * Тестване на индексите в базата данни
     */
    private function testDatabaseIndexes() {
        $tables_with_indexes = [
            'fake_customer_log' => ['customer_id', 'status', 'date_detected'],
            'fake_blacklist' => ['type', 'date_added'],
            'fake_detector_settings' => ['setting_key']
        ];

        foreach ($tables_with_indexes as $table => $indexes) {
            $full_table_name = DB_PREFIX . $table;
            
            try {
                $result = $this->db->query("SHOW INDEX FROM {$full_table_name}");
                $existing_indexes = array_column($result->rows, 'Column_name');
                
                foreach ($indexes as $index) {
                    if (in_array($index, $existing_indexes)) {
                        $this->addTestResult('Database Indexes', "✅ {$table}.{$index}", true);
                    } else {
                        $this->addTestResult('Database Indexes', "⚠️ {$table}.{$index} - препоръчителен индекс", false);
                    }
                }
            } catch (Exception $e) {
                $this->addTestResult('Database Indexes', "❌ {$table} - грешка при проверка на индекси", false);
            }
        }
    }

    /**
     * Тестове за контролерите
     */
    private function testControllers() {
        echo "🎮 Тестване на контролерите...\n";

        // Тестване на главния контролер
        $this->testMainController();

        // Тестване на sub-controllers
        $this->testSubControllers();
    }

    /**
     * Тестване на главния контролер
     */
    private function testMainController() {
        $controller_file = DIR_SYSTEM . '../system/storage/theme/Backend/Controller/Customer/Fakedetector.php';
        
        if (file_exists($controller_file)) {
            $content = file_get_contents($controller_file);
            
            // Проверка за dispatcher методи
            $required_methods = ['index', 'scan', 'blacklist', 'approve', 'delete'];
            
            foreach ($required_methods as $method) {
                if (strpos($content, "public function {$method}()") !== false) {
                    $this->addTestResult('Main Controller', "✅ {$method}() метод", true);
                } else {
                    $this->addTestResult('Main Controller', "❌ {$method}() метод - липсва", false);
                }
            }
            
            // Проверка за setBackendSubController използване
            if (strpos($content, 'setBackendSubController') !== false) {
                $this->addTestResult('Main Controller', "✅ setBackendSubController интеграция", true);
            } else {
                $this->addTestResult('Main Controller', "❌ setBackendSubController - не се използва", false);
            }
        } else {
            $this->addTestResult('Main Controller', "❌ Главният контролер не съществува", false);
        }
    }

    /**
     * Тестване на sub-controllers
     */
    private function testSubControllers() {
        $sub_controllers = ['Index', 'Scan', 'Blacklist', 'Approve', 'Delete'];
        
        foreach ($sub_controllers as $controller) {
            $file_path = DIR_SYSTEM . '../system/storage/theme/Backend/Controller/Customer/Fakedetector/' . $controller . '.php';
            
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                // Проверка за execute() метод
                if (strpos($content, 'public function execute()') !== false) {
                    $this->addTestResult('Sub Controllers', "✅ {$controller} - execute() метод", true);
                } else {
                    $this->addTestResult('Sub Controllers', "❌ {$controller} - липсва execute() метод", false);
                }
                
                // Проверка за constructor
                if (strpos($content, 'public function __construct($controller)') !== false) {
                    $this->addTestResult('Sub Controllers', "✅ {$controller} - правилен constructor", true);
                } else {
                    $this->addTestResult('Sub Controllers', "❌ {$controller} - грешен constructor", false);
                }
            } else {
                $this->addTestResult('Sub Controllers', "❌ {$controller} - файлът не съществува", false);
            }
        }
    }

    /**
     * Тестове за моделите
     */
    private function testModels() {
        echo "📊 Тестване на моделите...\n";

        $model_file = DIR_SYSTEM . '../system/storage/theme/Backend/Model/Customer/Fakedetector.php';
        
        if (file_exists($model_file)) {
            $content = file_get_contents($model_file);
            
            // Проверка за основни методи
            $required_methods = [
                'runScan', 'getFlaggedCustomers', 'approveCustomer', 
                'deleteCustomer', 'getBlacklist', 'addToBlacklist'
            ];
            
            foreach ($required_methods as $method) {
                if (strpos($content, "public function {$method}(") !== false) {
                    $this->addTestResult('Models', "✅ {$method}() метод", true);
                } else {
                    $this->addTestResult('Models', "❌ {$method}() метод - липсва", false);
                }
            }
        } else {
            $this->addTestResult('Models', "❌ Модел файлът не съществува", false);
        }
    }

    /**
     * Тестове за helper класовете
     */
    private function testHelpers() {
        echo "🔧 Тестване на helper класовете...\n";

        if ($this->helper) {
            // Тест за основни методи
            try {
                $settings = $this->helper->getAllSettings();
                $this->addTestResult('Helpers', "✅ getAllSettings() работи", true);
            } catch (Exception $e) {
                $this->addTestResult('Helpers', "❌ getAllSettings() - грешка: " . $e->getMessage(), false);
            }

            // Тест за валидация
            try {
                $is_valid_ip = $this->helper->isValidIp('***********');
                $this->addTestResult('Helpers', "✅ isValidIp() работи", true);
            } catch (Exception $e) {
                $this->addTestResult('Helpers', "❌ isValidIp() - грешка: " . $e->getMessage(), false);
            }
        } else {
            $this->addTestResult('Helpers', "❌ Helper класът не е инициализиран", false);
        }
    }

    /**
     * Тестове за JavaScript файловете
     */
    private function testJavaScript() {
        echo "🔧 Тестване на JavaScript файловете...\n";

        $js_files = [
            'fake-detector.js',
            'fake-detector-blacklist.js',
            'fake-detector-scan.js'
        ];

        foreach ($js_files as $js_file) {
            $file_path = DIR_SYSTEM . '../system/storage/theme/Backend/View/Javascript/' . $js_file;
            
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                // Проверка за IIFE pattern
                if (strpos($content, '(function()') !== false && strpos($content, '})();') !== false) {
                    $this->addTestResult('JavaScript', "✅ {$js_file} - IIFE pattern", true);
                } else {
                    $this->addTestResult('JavaScript', "❌ {$js_file} - липсва IIFE pattern", false);
                }
                
                // Проверка за DOMContentLoaded
                if (strpos($content, 'DOMContentLoaded') !== false) {
                    $this->addTestResult('JavaScript', "✅ {$js_file} - DOMContentLoaded", true);
                } else {
                    $this->addTestResult('JavaScript', "❌ {$js_file} - липсва DOMContentLoaded", false);
                }
            } else {
                $this->addTestResult('JavaScript', "❌ {$js_file} - файлът не съществува", false);
            }
        }
    }

    /**
     * Тестове за templates
     */
    private function testTemplates() {
        echo "🎨 Тестване на templates...\n";

        $template_files = [
            'fake_detector.twig',
            'fake_detector_blacklist.twig',
            'fake_detector_manual_scan.twig'
        ];

        foreach ($template_files as $template) {
            $file_path = DIR_SYSTEM . '../system/storage/theme/Backend/View/Template/customer/' . $template;
            
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                // Проверка за основни Twig елементи
                if (strpos($content, '{{ header }}') !== false && strpos($content, '{{ footer }}') !== false) {
                    $this->addTestResult('Templates', "✅ {$template} - правилна структура", true);
                } else {
                    $this->addTestResult('Templates', "❌ {$template} - грешна структура", false);
                }
            } else {
                $this->addTestResult('Templates', "❌ {$template} - файлът не съществува", false);
            }
        }
    }

    /**
     * Тестове за конфигурацията
     */
    private function testConfiguration() {
        echo "⚙️ Тестване на конфигурацията...\n";

        $config_file = DIR_SYSTEM . '../system/storage/theme/Backend/Config/fakedetector_config.php';
        
        if (file_exists($config_file)) {
            $config = include $config_file;
            
            if (is_array($config) && isset($config['module_info'])) {
                $this->addTestResult('Configuration', "✅ Конфигурационният файл е валиден", true);
            } else {
                $this->addTestResult('Configuration', "❌ Конфигурационният файл е невалиден", false);
            }
        } else {
            $this->addTestResult('Configuration', "❌ Конфигурационният файл не съществува", false);
        }
    }

    /**
     * Тестове за функционалността
     */
    private function testFunctionality() {
        echo "🚀 Тестване на функционалността...\n";

        // Тест за инсталация
        if ($this->helper && $this->helper->isModuleInstalled()) {
            $this->addTestResult('Functionality', "✅ Модулът е инсталиран", true);
        } else {
            $this->addTestResult('Functionality', "❌ Модулът не е инсталиран", false);
        }

        // Тест за настройки
        try {
            $version = $this->helper->getModuleVersion();
            $this->addTestResult('Functionality', "✅ Версия: {$version}", true);
        } catch (Exception $e) {
            $this->addTestResult('Functionality', "❌ Грешка при получаване на версия", false);
        }
    }

    /**
     * Добавя резултат от тест
     */
    private function addTestResult($category, $message, $success) {
        if (!isset($this->test_results[$category])) {
            $this->test_results[$category] = [];
        }
        
        $this->test_results[$category][] = [
            'message' => $message,
            'success' => $success
        ];
        
        if (!$success) {
            $this->errors[] = "{$category}: {$message}";
        }
    }

    /**
     * Показва резултатите от тестовете
     */
    private function displayResults() {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 РЕЗУЛТАТИ ОТ ТЕСТОВЕТЕ\n";
        echo str_repeat("=", 60) . "\n\n";

        $total_tests = 0;
        $passed_tests = 0;

        foreach ($this->test_results as $category => $tests) {
            echo "📂 {$category}:\n";
            
            foreach ($tests as $test) {
                echo "   {$test['message']}\n";
                $total_tests++;
                if ($test['success']) {
                    $passed_tests++;
                }
            }
            echo "\n";
        }

        echo str_repeat("-", 60) . "\n";
        echo "📈 ОБОБЩЕНИЕ: {$passed_tests}/{$total_tests} тестове преминаха успешно\n";
        
        if (count($this->errors) > 0) {
            echo "❌ ГРЕШКИ:\n";
            foreach ($this->errors as $error) {
                echo "   • {$error}\n";
            }
        } else {
            echo "✅ Всички тестове преминаха успешно!\n";
        }
        
        echo str_repeat("=", 60) . "\n";
    }

    /**
     * Връща обобщение на тестовете
     */
    private function getTestSummary() {
        $total_tests = 0;
        $passed_tests = 0;

        foreach ($this->test_results as $tests) {
            foreach ($tests as $test) {
                $total_tests++;
                if ($test['success']) {
                    $passed_tests++;
                }
            }
        }

        return [
            'total_tests' => $total_tests,
            'passed_tests' => $passed_tests,
            'failed_tests' => $total_tests - $passed_tests,
            'success_rate' => $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0,
            'errors' => $this->errors,
            'all_passed' => count($this->errors) === 0
        ];
    }
}
