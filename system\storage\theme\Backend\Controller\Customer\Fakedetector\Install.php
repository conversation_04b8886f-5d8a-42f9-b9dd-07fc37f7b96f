<?php

namespace Theme25\Backend\Controller\Customer\Fakedetector;

class Install extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Инсталира таблиците за Fake Detector модула
     */
    public function execute() {
        $json = [];

        try {
            $this->createTables();
            $json['success'] = 'Таблиците за Fake Detector модула са създадени успешно!';
        } catch (Exception $e) {
            $json['error'] = 'Грешка при създаване на таблиците: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Създава необходимите таблици за модула
     */
    private function createTables() {
        // Таблица за маркирани потребители
        $sql_customer_log = "
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "fake_customer_log` (
                `id` INT NOT NULL AUTO_INCREMENT,
                `customer_id` INT NOT NULL,
                `email` VARCHAR(255),
                `ip` VARCHAR(45),
                `reason` TEXT,
                `date_detected` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `status` ENUM('pending','approved','deleted') DEFAULT 'pending',
                PRIMARY KEY (`id`),
                KEY `customer_id` (`customer_id`),
                KEY `status` (`status`),
                KEY `date_detected` (`date_detected`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        // Таблица за черен списък (IP или Email)
        $sql_blacklist = "
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "fake_blacklist` (
                `id` INT NOT NULL AUTO_INCREMENT,
                `type` ENUM('ip', 'email') NOT NULL,
                `value` VARCHAR(255) NOT NULL,
                `note` VARCHAR(255),
                `date_added` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `type_value` (`type`, `value`),
                KEY `type` (`type`),
                KEY `date_added` (`date_added`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        // Таблица за настройки на модула
        $sql_settings = "
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "fake_detector_settings` (
                `id` INT NOT NULL AUTO_INCREMENT,
                `setting_key` VARCHAR(100) NOT NULL,
                `setting_value` TEXT,
                `date_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `setting_key` (`setting_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        // Изпълняване на заявките
        $this->dbQuery($sql_customer_log);
        $this->dbQuery($sql_blacklist);
        $this->dbQuery($sql_settings);

        // Добавяне на начални настройки
        $this->insertDefaultSettings();
    }

    /**
     * Добавя начални настройки за модула
     */
    private function insertDefaultSettings() {
        $default_settings = [
            'min_username_length' => '10',
            'regex_username' => '/^[a-zA-Z0-9]{10,}$/',
            'bad_domains' => json_encode(['hotmail.com', 'mail.ru', 'yopmail.com', '10minutemail.com']),
            'max_accounts_per_ip' => '3',
            'only_no_orders' => '1',
            'check_blacklist' => '1',
            'test_mode' => '1',
            'auto_scan_enabled' => '0',
            'scan_interval_hours' => '24'
        ];

        foreach ($default_settings as $key => $value) {
            $sql = "INSERT IGNORE INTO `" . DB_PREFIX . "fake_detector_settings` 
                    (`setting_key`, `setting_value`) 
                    VALUES ('" . $this->dbEscape($key) . "', '" . $this->dbEscape($value) . "')";
            $this->dbQuery($sql);
        }
    }

    /**
     * Проверява дали таблиците съществуват
     */
    public function checkTables() {
        $tables = [
            DB_PREFIX . 'fake_customer_log',
            DB_PREFIX . 'fake_blacklist',
            DB_PREFIX . 'fake_detector_settings'
        ];

        $existing_tables = [];
        foreach ($tables as $table) {
            $result = $this->dbQuery("SHOW TABLES LIKE '" . $table . "'");
            if ($result->num_rows > 0) {
                $existing_tables[] = $table;
            }
        }

        return [
            'total' => count($tables),
            'existing' => count($existing_tables),
            'missing' => array_diff($tables, $existing_tables),
            'all_exist' => count($existing_tables) === count($tables)
        ];
    }

    /**
     * Премахва таблиците на модула (за деинсталация)
     */
    public function uninstall() {
        $json = [];

        try {
            $this->dbQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "fake_customer_log`");
            $this->dbQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "fake_blacklist`");
            $this->dbQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "fake_detector_settings`");
            
            $json['success'] = 'Таблиците за Fake Detector модула са премахнати успешно!';
        } catch (Exception $e) {
            $json['error'] = 'Грешка при премахване на таблиците: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }
}
