<!-- Fake Detector Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
  <div class="flex flex-col md:flex-row md:items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Детектор на фалшиви регистрации</h1>
      <p class="text-gray-500 mt-1">Управление и откриване на подозрителни потребителски регистрации</p>
    </div>
    <div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
      <button id="button-scan" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-search-line"></i>
        </div>
        <span>Сканирай</span>
      </button>
      <a href="{{ manual_scan_url }}" class="px-4 py-2 bg-blue-600 text-white rounded-button hover:bg-blue-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-settings-3-line"></i>
        </div>
        <span>Ръчно сканиране</span>
      </a>
      <a href="{{ blacklist_url }}" class="px-4 py-2 bg-orange-600 text-white rounded-button hover:bg-orange-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-forbid-line"></i>
        </div>
        <span>Черен списък</span>
      </a>
      <a href="{{ settings_url }}" class="px-4 py-2 bg-gray-600 text-white rounded-button hover:bg-gray-700 transition-colors whitespace-nowrap flex items-center">
        <div class="w-5 h-5 flex items-center justify-center mr-2">
          <i class="ri-settings-line"></i>
        </div>
        <span>Настройки</span>
      </a>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="p-6">
  <!-- Alert Messages -->
  {% if error_warning %}
  <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-error-warning-line text-red-500 mr-2"></i>
      <span class="text-red-700">{{ error_warning }}</span>
    </div>
  </div>
  {% endif %}

  {% if success %}
  <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
    <div class="flex items-center">
      <i class="ri-check-line text-green-500 mr-2"></i>
      <span class="text-green-700">{{ success }}</span>
    </div>
  </div>
  {% endif %}

  <!-- Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Общо маркирани</p>
          <p class="text-2xl font-bold text-gray-900">{{ stats.total_flagged|default(0) }}</p>
        </div>
        <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
          <i class="ri-user-line text-primary text-xl"></i>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Чакащи одобрение</p>
          <p class="text-2xl font-bold text-orange-600">{{ stats.pending|default(0) }}</p>
        </div>
        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
          <i class="ri-time-line text-orange-600 text-xl"></i>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Одобрени</p>
          <p class="text-2xl font-bold text-green-600">{{ stats.approved|default(0) }}</p>
        </div>
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
          <i class="ri-check-line text-green-600 text-xl"></i>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Изтрити</p>
          <p class="text-2xl font-bold text-red-600">{{ stats.deleted|default(0) }}</p>
        </div>
        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
          <i class="ri-delete-bin-line text-red-600 text-xl"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Table Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 flex items-center">
        <i class="ri-user-search-line mr-2"></i>
        Маркирани потребители
      </h3>
    </div>
    <div class="p-6">
      <!-- Filters -->
      <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-status">Статус</label>
            <select name="filter_status" id="input-status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
              {% for key, value in status_options %}
              <option value="{{ key }}"{% if filter_status == key %} selected{% endif %}>{{ value }}</option>
              {% endfor %}
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-email">Email</label>
            <input type="text" name="filter_email" value="{{ filter_email }}" placeholder="Email адрес" id="input-email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-ip">IP адрес</label>
            <input type="text" name="filter_ip" value="{{ filter_ip }}" placeholder="IP адрес" id="input-ip" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-reason">Причина</label>
            <input type="text" name="filter_reason" value="{{ filter_reason }}" placeholder="Причина за маркиране" id="input-reason" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-date-from">От дата</label>
            <div class="relative">
              <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="От дата" id="input-date-from" class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <i class="ri-calendar-line text-gray-400"></i>
              </div>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2" for="input-date-to">До дата</label>
            <div class="relative">
              <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="До дата" id="input-date-to" class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <i class="ri-calendar-line text-gray-400"></i>
              </div>
            </div>
          </div>
          <div class="flex items-end">
            <div class="flex gap-2 w-full">
              <button type="button" id="button-filter" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center">
                <i class="ri-search-line mr-2"></i>
                Филтрирай
              </button>
              <button type="button" id="button-clear" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center">
                <i class="ri-refresh-line mr-2"></i>
                Изчисти
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Results Table -->
      <form id="form-customer" method="post">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                  <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {% if sort == 'customer_id' %}
                  <a href="{{ sort_customer_id }}" class="flex items-center hover:text-gray-700">
                    ID
                    <i class="ri-arrow-up-down-line ml-1 {% if order == 'ASC' %}text-primary{% else %}text-primary{% endif %}"></i>
                  </a>
                  {% else %}
                  <a href="{{ sort_customer_id }}" class="flex items-center hover:text-gray-700">
                    ID
                    <i class="ri-arrow-up-down-line ml-1"></i>
                  </a>
                  {% endif %}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {% if sort == 'email' %}
                  <a href="{{ sort_email }}" class="flex items-center hover:text-gray-700">
                    Email
                    <i class="ri-arrow-up-down-line ml-1 {% if order == 'ASC' %}text-primary{% else %}text-primary{% endif %}"></i>
                  </a>
                  {% else %}
                  <a href="{{ sort_email }}" class="flex items-center hover:text-gray-700">
                    Email
                    <i class="ri-arrow-up-down-line ml-1"></i>
                  </a>
                  {% endif %}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  IP адрес
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Причина
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {% if sort == 'date_detected' %}
                  <a href="{{ sort_date_detected }}" class="flex items-center hover:text-gray-700">
                    Дата на откриване
                    <i class="ri-arrow-up-down-line ml-1 {% if order == 'ASC' %}text-primary{% else %}text-primary{% endif %}"></i>
                  </a>
                  {% else %}
                  <a href="{{ sort_date_detected }}" class="flex items-center hover:text-gray-700">
                    Дата на откриване
                    <i class="ri-arrow-up-down-line ml-1"></i>
                  </a>
                  {% endif %}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {% if sort == 'status' %}
                  <a href="{{ sort_status }}" class="flex items-center hover:text-gray-700">
                    Статус
                    <i class="ri-arrow-up-down-line ml-1 {% if order == 'ASC' %}text-primary{% else %}text-primary{% endif %}"></i>
                  </a>
                  {% else %}
                  <a href="{{ sort_status }}" class="flex items-center hover:text-gray-700">
                    Статус
                    <i class="ri-arrow-up-down-line ml-1"></i>
                  </a>
                  {% endif %}
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Действия
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% if customers %}
              {% for customer in customers %}
              <tr class="hover:bg-gray-50 transition-colors" data-customer-id="{{ customer.customer_id }}">
                <td class="px-6 py-4 whitespace-nowrap">
                  {% if customer.status == 'pending' %}
                  <input type="checkbox" name="selected[]" value="{{ customer.customer_id }}" class="rounded border-gray-300 text-primary focus:ring-primary" />
                  {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ customer.customer_id }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <a href="{{ customer.customer_url }}" target="_blank" class="text-primary hover:text-primary/80 text-sm">
                    {{ customer.email }}
                  </a>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ customer.ip }}
                </td>
                <td class="px-6 py-4 text-sm text-gray-900">
                  <div class="max-w-xs truncate" title="{{ customer.reason }}">
                    {{ customer.reason }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ customer.date_detected }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {% if customer.status == 'pending' %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                    {{ customer.status_text }}
                  </span>
                  {% elseif customer.status == 'approved' %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    {{ customer.status_text }}
                  </span>
                  {% elseif customer.status == 'deleted' %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    {{ customer.status_text }}
                  </span>
                  {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  {% if customer.status == 'pending' %}
                  <div class="flex justify-end gap-2">
                    <a href="{{ customer.approve_url }}" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors" title="Одобри">
                      <i class="ri-check-line mr-1"></i>
                      Одобри
                    </a>
                    <a href="{{ customer.delete_url }}" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 transition-colors" title="Изтрий" onclick="return confirm('Сигурен ли сте?');">
                      <i class="ri-delete-bin-line mr-1"></i>
                      Изтрий
                    </a>
                  </div>
                  {% else %}
                  <span class="text-gray-400">-</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                  <div class="flex flex-col items-center">
                    <i class="ri-user-search-line text-4xl text-gray-300 mb-2"></i>
                    <p class="text-lg font-medium">Няма маркирани потребители</p>
                    <p class="text-sm">Стартирайте сканиране за да откриете подозрителни регистрации</p>
                  </div>
                </td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
      </form>

      <!-- Bulk Actions and Pagination -->
      {% if customers %}
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="flex gap-2">
            <button type="button" id="button-approve-selected" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors">
              <i class="ri-check-line mr-2"></i>
              Одобри избраните
            </button>
            <button type="button" id="button-delete-selected" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 transition-colors">
              <i class="ri-delete-bin-line mr-2"></i>
              Изтрий избраните
            </button>
          </div>
          <div class="flex items-center justify-between sm:justify-end gap-4">
            <div class="text-sm text-gray-700">
              {{ results }}
            </div>
            <div>
              {{ pagination }}
            </div>
          </div>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div id="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg p-6 flex items-center">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-3"></div>
    <span class="text-gray-700">Зареждане...</span>
  </div>
</div>

<script>
// Configuration for the page
window.fakeDetectorConfig = {
  userToken: '{{ user_token }}',
  scanUrl: '{{ scan_url }}',
  approveUrl: '{{ approve_url }}',
  deleteUrl: '{{ delete_url }}',
  blacklistUrl: '{{ blacklist_url }}',
  validateUrl: '{{ validate_url }}',
  currentSort: '{{ sort }}',
  currentOrder: '{{ order }}'
};
</script>
