{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ scan_url }}" class="btn btn-primary" data-toggle="tooltip" title="Автоматично сканиране">
          <i class="fa fa-search"></i> Сканирай
        </a>
        <a href="{{ manual_scan_url }}" class="btn btn-info" data-toggle="tooltip" title="Ръчно сканиране">
          <i class="fa fa-cogs"></i> Ръчно сканиране
        </a>
        <a href="{{ blacklist_url }}" class="btn btn-warning" data-toggle="tooltip" title="Черен списък">
          <i class="fa fa-ban"></i> Черен списък
        </a>
        <a href="{{ settings_url }}" class="btn btn-default" data-toggle="tooltip" title="Настройки">
          <i class="fa fa-cog"></i> Настройки
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> Маркирани потребители</h3>
      </div>
      <div class="panel-body">
        
        <!-- Филтри -->
        <div class="well">
          <div class="row">
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-status">Статус</label>
                <select name="filter_status" id="input-status" class="form-control">
                  {% for key, value in status_options %}
                  <option value="{{ key }}"{% if filter_status == key %} selected{% endif %}>{{ value }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-email">Email</label>
                <input type="text" name="filter_email" value="{{ filter_email }}" placeholder="Email адрес" id="input-email" class="form-control" />
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-ip">IP адрес</label>
                <input type="text" name="filter_ip" value="{{ filter_ip }}" placeholder="IP адрес" id="input-ip" class="form-control" />
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-reason">Причина</label>
                <input type="text" name="filter_reason" value="{{ filter_reason }}" placeholder="Причина за маркиране" id="input-reason" class="form-control" />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-date-from">От дата</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="От дата" id="input-date-from" class="form-control" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-date-to">До дата</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="До дата" id="input-date-to" class="form-control" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <div class="btn-group btn-group-sm" style="display: block;">
                  <button type="button" id="button-filter" class="btn btn-primary">
                    <i class="fa fa-search"></i> Филтрирай
                  </button>
                  <button type="button" id="button-clear" class="btn btn-default">
                    <i class="fa fa-refresh"></i> Изчисти
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Таблица с резултати -->
        <form id="form-customer" method="post">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td class="text-left">
                    {% if sort == 'customer_id' %}
                    <a href="{{ sort_customer_id }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">ID</a>
                    {% else %}
                    <a href="{{ sort_customer_id }}">ID</a>
                    {% endif %}
                  </td>
                  <td class="text-left">
                    {% if sort == 'email' %}
                    <a href="{{ sort_email }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">Email</a>
                    {% else %}
                    <a href="{{ sort_email }}">Email</a>
                    {% endif %}
                  </td>
                  <td class="text-left">IP адрес</td>
                  <td class="text-left">Причина</td>
                  <td class="text-left">
                    {% if sort == 'date_detected' %}
                    <a href="{{ sort_date_detected }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">Дата на откриване</a>
                    {% else %}
                    <a href="{{ sort_date_detected }}">Дата на откриване</a>
                    {% endif %}
                  </td>
                  <td class="text-left">
                    {% if sort == 'status' %}
                    <a href="{{ sort_status }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">Статус</a>
                    {% else %}
                    <a href="{{ sort_status }}">Статус</a>
                    {% endif %}
                  </td>
                  <td class="text-right">Действия</td>
                </tr>
              </thead>
              <tbody>
                {% if customers %}
                {% for customer in customers %}
                <tr>
                  <td class="text-center">
                    {% if customer.status == 'pending' %}
                    <input type="checkbox" name="selected[]" value="{{ customer.customer_id }}" />
                    {% endif %}
                  </td>
                  <td class="text-left">{{ customer.customer_id }}</td>
                  <td class="text-left">
                    <a href="{{ customer.customer_url }}" target="_blank">{{ customer.email }}</a>
                  </td>
                  <td class="text-left">{{ customer.ip }}</td>
                  <td class="text-left">{{ customer.reason }}</td>
                  <td class="text-left">{{ customer.date_detected }}</td>
                  <td class="text-left">
                    {% if customer.status == 'pending' %}
                    <span class="label label-warning">{{ customer.status_text }}</span>
                    {% elseif customer.status == 'approved' %}
                    <span class="label label-success">{{ customer.status_text }}</span>
                    {% elseif customer.status == 'deleted' %}
                    <span class="label label-danger">{{ customer.status_text }}</span>
                    {% endif %}
                  </td>
                  <td class="text-right">
                    {% if customer.status == 'pending' %}
                    <div class="btn-group">
                      <a href="{{ customer.approve_url }}" class="btn btn-success btn-xs" data-toggle="tooltip" title="Одобри">
                        <i class="fa fa-check"></i>
                      </a>
                      <a href="{{ customer.delete_url }}" class="btn btn-danger btn-xs" data-toggle="tooltip" title="Изтрий" onclick="return confirm('Сигурен ли сте?');">
                        <i class="fa fa-trash"></i>
                      </a>
                    </div>
                    {% else %}
                    <span class="text-muted">-</span>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="8">Няма маркирани потребители.</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>

        <!-- Масови действия -->
        {% if customers %}
        <div class="row">
          <div class="col-sm-6">
            <div class="btn-group">
              <button type="button" id="button-approve-selected" class="btn btn-success">
                <i class="fa fa-check"></i> Одобри избраните
              </button>
              <button type="button" id="button-delete-selected" class="btn btn-danger">
                <i class="fa fa-trash"></i> Изтрий избраните
              </button>
            </div>
          </div>
          <div class="col-sm-6 text-right">{{ pagination }}</div>
        </div>
        <div class="row">
          <div class="col-sm-6">
            <div class="text-muted">{{ results }}</div>
          </div>
        </div>
        {% endif %}

      </div>
    </div>
  </div>
</div>

{{ footer }}
