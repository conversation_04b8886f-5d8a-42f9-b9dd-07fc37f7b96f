<?php

namespace Theme25\Backend\Helper;

/**
 * Fake Detector Helper Class
 * Централизиран helper клас за функционалностите на Fake Detector модула
 * (адаптирано от оригиналния fake_detector_rules.php)
 */
class Fakedetectorhelper {

    private $db;
    private $config;
    private $settings;
    private $cache;

    public function __construct($db, $config = null) {
        $this->db = $db;
        $this->config = $config;
        $this->cache = [];
        $this->loadSettings();
    }

    /**
     * Зарежда настройките на модула
     */
    private function loadSettings() {
        if (isset($this->cache['settings'])) {
            $this->settings = $this->cache['settings'];
            return;
        }

        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "fake_detector_settings");
        
        $settings = [];
        foreach ($query->rows as $row) {
            $value = $row['setting_value'];
            
            // Опит за декодиране на JSON
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $value = $decoded;
            }
            
            $settings[$row['setting_key']] = $value;
        }
        
        // Обединяване с настройки по подразбиране
        $this->settings = array_merge($this->getDefaultSettings(), $settings);
        $this->cache['settings'] = $this->settings;
    }

    /**
     * Връща настройките по подразбиране
     */
    private function getDefaultSettings() {
        return [
            'min_username_length' => 10,
            'regex_username' => '/^[a-zA-Z0-9]{10,}$/',
            'bad_domains' => [
                'hotmail.com', 'mail.ru', 'yopmail.com', '10minutemail.com',
                'guerrillamail.com', 'tempmail.org', 'mailinator.com',
                'throwaway.email', 'getnada.com', 'temp-mail.org'
            ],
            'max_accounts_per_ip' => 3,
            'only_no_orders' => true,
            'check_blacklist' => true,
            'test_mode' => true,
            'auto_scan_enabled' => false,
            'scan_interval_hours' => 24,
            'delete_after_days' => 30
        ];
    }

    /**
     * Получава настройка по ключ
     */
    public function getSetting($key, $default = null) {
        return isset($this->settings[$key]) ? $this->settings[$key] : $default;
    }

    /**
     * Получава всички настройки
     */
    public function getAllSettings() {
        return $this->settings;
    }

    /**
     * Записва настройка
     */
    public function setSetting($key, $value) {
        $value_json = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
        
        $sql = "INSERT INTO " . DB_PREFIX . "fake_detector_settings 
                (setting_key, setting_value) 
                VALUES ('" . $this->db->escape($key) . "', '" . $this->db->escape($value_json) . "')
                ON DUPLICATE KEY UPDATE 
                setting_value = '" . $this->db->escape($value_json) . "',
                date_modified = NOW()";
        
        $this->db->query($sql);
        
        // Обновяване на кеша
        $this->settings[$key] = $value;
        $this->cache['settings'] = $this->settings;
        
        return true;
    }

    /**
     * Проверява дали името е подозрително
     * (адаптирано от оригиналните правила)
     */
    public function isSuspiciousName($firstname) {
        $min_length = (int)$this->getSetting('min_username_length', 10);
        $regex = $this->getSetting('regex_username', '/^[a-zA-Z0-9]{10,}$/');
        
        // Проверка на дължината
        if (strlen($firstname) < $min_length) {
            return false;
        }
        
        // Проверка с regex
        if (!empty($regex) && preg_match($regex, $firstname)) {
            return true;
        }
        
        // Допълнителни проверки
        return $this->checkAdditionalNameRules($firstname);
    }

    /**
     * Допълнителни правила за проверка на имена
     */
    private function checkAdditionalNameRules($firstname) {
        // Проверка за повтарящи се символи
        if (preg_match('/(.)\1{3,}/', $firstname)) {
            return true;
        }
        
        // Проверка за последователни числа
        if (preg_match('/\d{4,}/', $firstname)) {
            return true;
        }
        
        // Проверка за смесица от главни и малки букви без логика
        if (preg_match('/[a-z][A-Z][a-z][A-Z]/', $firstname)) {
            return true;
        }
        
        return false;
    }

    /**
     * Проверява дали домейнът е съмнителен
     */
    public function isBadDomain($domain) {
        $bad_domains = $this->getSetting('bad_domains', []);
        
        if (!is_array($bad_domains)) {
            return false;
        }
        
        $domain = strtolower($domain);
        
        // Точно съвпадение
        if (in_array($domain, array_map('strtolower', $bad_domains))) {
            return true;
        }
        
        // Проверка за поддомейни
        foreach ($bad_domains as $bad_domain) {
            if (strpos($domain, strtolower($bad_domain)) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Проверява дали IP адресът има много регистрации
     */
    public function hasMultipleRegistrations($ip) {
        $max_accounts = (int)$this->getSetting('max_accounts_per_ip', 3);
        
        $query = $this->db->query("SELECT COUNT(*) as total 
                                  FROM " . DB_PREFIX . "customer 
                                  WHERE ip = '" . $this->db->escape($ip) . "'");
        
        return (int)$query->row['total'] >= $max_accounts;
    }

    /**
     * Проверява дали потребителят няма поръчки
     */
    public function hasNoOrders($customer_id) {
        if (!$this->getSetting('only_no_orders', true)) {
            return false;
        }
        
        $query = $this->db->query("SELECT COUNT(*) as total 
                                  FROM " . DB_PREFIX . "order 
                                  WHERE customer_id = " . (int)$customer_id);
        
        return (int)$query->row['total'] === 0;
    }

    /**
     * Проверява в черния списък
     */
    public function isInBlacklist($ip, $email) {
        if (!$this->getSetting('check_blacklist', true)) {
            return false;
        }
        
        $domain = substr(strrchr($email, "@"), 1);
        
        // Проверка на IP
        $query = $this->db->query("SELECT COUNT(*) as total 
                                  FROM " . DB_PREFIX . "fake_blacklist 
                                  WHERE type = 'ip' AND value = '" . $this->db->escape($ip) . "'");
        
        if ((int)$query->row['total'] > 0) {
            return true;
        }
        
        // Проверка на email/домейн
        $query = $this->db->query("SELECT COUNT(*) as total 
                                  FROM " . DB_PREFIX . "fake_blacklist 
                                  WHERE type = 'email' AND (value = '" . $this->db->escape($email) . "' OR value = '" . $this->db->escape($domain) . "')");
        
        return (int)$query->row['total'] > 0;
    }

    /**
     * Анализира потребител и връща причините за маркиране
     */
    public function analyzeCustomer($customer) {
        $reasons = [];
        
        $firstname = $customer['firstname'];
        $email = strtolower($customer['email']);
        $domain = substr(strrchr($email, "@"), 1);
        $ip = $customer['ip'];
        $customer_id = (int)$customer['customer_id'];
        
        // Проверка за подозрителни имена
        if ($this->isSuspiciousName($firstname)) {
            $reasons[] = "Подозрително име";
        }
        
        // Проверка за съмнителни домейни
        if ($this->isBadDomain($domain)) {
            $reasons[] = "Съмнителен домейн ($domain)";
        }
        
        // Проверка за множество регистрации от един IP
        if ($this->hasMultipleRegistrations($ip)) {
            $count = $this->getIpRegistrationCount($ip);
            $reasons[] = "IP с много регистрации ($count)";
        }
        
        // Проверка за акаунти без поръчки
        if ($this->hasNoOrders($customer_id)) {
            $reasons[] = "Няма поръчки";
        }
        
        // Проверка в черния списък
        if ($this->isInBlacklist($ip, $email)) {
            $reasons[] = "В черен списък";
        }
        
        return $reasons;
    }

    /**
     * Получава броя регистрации от IP адрес
     */
    public function getIpRegistrationCount($ip) {
        $query = $this->db->query("SELECT COUNT(*) as total 
                                  FROM " . DB_PREFIX . "customer 
                                  WHERE ip = '" . $this->db->escape($ip) . "'");
        return (int)$query->row['total'];
    }

    /**
     * Валидира IP адрес
     */
    public function isValidIp($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * Валидира email домейн
     */
    public function isValidDomain($domain) {
        return filter_var('test@' . $domain, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Получава статистики за модула
     */
    public function getStatistics() {
        $stats = [];
        
        // Общо маркирани потребители
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "fake_customer_log");
        $stats['total_flagged'] = (int)$query->row['total'];
        
        // По статуси
        $query = $this->db->query("SELECT status, COUNT(*) as count 
                                  FROM " . DB_PREFIX . "fake_customer_log 
                                  GROUP BY status");
        foreach ($query->rows as $row) {
            $stats['by_status'][$row['status']] = (int)$row['count'];
        }
        
        // Записи в черния списък
        $query = $this->db->query("SELECT type, COUNT(*) as count 
                                  FROM " . DB_PREFIX . "fake_blacklist 
                                  GROUP BY type");
        foreach ($query->rows as $row) {
            $stats['blacklist'][$row['type']] = (int)$row['count'];
        }
        
        // Последно сканиране
        $query = $this->db->query("SELECT MAX(date_detected) as last_scan 
                                  FROM " . DB_PREFIX . "fake_customer_log");
        $stats['last_scan'] = $query->row['last_scan'];
        
        return $stats;
    }

    /**
     * Почиства стари записи
     */
    public function cleanOldRecords() {
        $delete_after_days = (int)$this->getSetting('delete_after_days', 30);
        
        if ($delete_after_days <= 0) {
            return 0;
        }
        
        $sql = "DELETE FROM " . DB_PREFIX . "fake_customer_log 
                WHERE status = 'deleted' 
                AND date_detected < DATE_SUB(NOW(), INTERVAL " . $delete_after_days . " DAY)";
        
        $this->db->query($sql);
        
        return $this->db->countAffected();
    }

    /**
     * Експортира настройките
     */
    public function exportSettings() {
        return json_encode($this->settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Импортира настройките
     */
    public function importSettings($json_data) {
        $settings = json_decode($json_data, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Невалиден JSON формат');
        }
        
        $imported = 0;
        foreach ($settings as $key => $value) {
            if ($this->setSetting($key, $value)) {
                $imported++;
            }
        }
        
        return $imported;
    }

    /**
     * Проверява дали модулът е правилно инсталиран
     */
    public function isModuleInstalled() {
        $tables = [
            DB_PREFIX . 'fake_customer_log',
            DB_PREFIX . 'fake_blacklist',
            DB_PREFIX . 'fake_detector_settings'
        ];
        
        foreach ($tables as $table) {
            $result = $this->db->query("SHOW TABLES LIKE '" . $table . "'");
            if ($result->num_rows === 0) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Получава версията на модула
     */
    public function getModuleVersion() {
        return $this->getSetting('module_version', '1.0.0');
    }

    /**
     * Обновява версията на модула
     */
    public function updateModuleVersion($version) {
        return $this->setSetting('module_version', $version);
    }
}
