/**
 * Fake Detector Settings Module JavaScript
 * Следва BackendModule pattern на Rakla.bg проекта
 */

(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initFakeDetectorSettings();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        Object.assign(BackendModule, {
            
            // Конфигурация за Settings
            fakeDetectorSettings: {
                config: null,
                elements: {},
                isInitialized: false,
                originalValues: {}
            },

            // Инициализация на Settings модула
            initFakeDetectorSettings: function() {
                if (this.fakeDetectorSettings.isInitialized) return;
                
                // Проверка дали сме на Settings страница
                if (!window.settingsConfig) return;
                
                this.fakeDetectorSettings.config = window.settingsConfig;
                this.cacheSettingsElements();
                this.bindSettingsEvents();
                this.initSettingsComponents();
                
                this.fakeDetectorSettings.isInitialized = true;
                this.log('Fake Detector Settings module initialized');
            },

            // Кеширане на DOM елементи
            cacheSettingsElements: function() {
                this.fakeDetectorSettings.elements = {
                    // Основни форми
                    settingsForm: document.getElementById('form-settings'),
                    
                    // Бутони за действия
                    resetButton: document.getElementById('button-reset-settings'),
                    testButton: document.getElementById('button-test-settings'),
                    exportButton: document.getElementById('button-export'),
                    importFileInput: document.getElementById('import-file'),

                    // Настройки за сканиране
                    scanIntervalInput: document.getElementById('scan-interval'),
                    maxAccountsPerIpInput: document.getElementById('max-accounts-per-ip'),
                    minUsernameLengthInput: document.getElementById('min-username-length'),
                    regexUsernameInput: document.getElementById('regex-username'),
                    badDomainsTextarea: document.getElementById('bad-domains'),
                    
                    // Правила за детекция
                    rulesCheckboxes: document.querySelectorAll('input[name="detection_rules[]"]'),
                    
                    // Email настройки
                    emailEnabledCheckbox: document.getElementById('setting-email-enabled'),
                    emailAddressInput: document.getElementById('setting-email-address'),
                    emailSubjectInput: document.getElementById('setting-email-subject'),
                    
                    // Лог настройки
                    logEnabledCheckbox: document.getElementById('setting-log-enabled'),
                    logLevelSelect: document.getElementById('setting-log-level'),
                    logRetentionInput: document.getElementById('setting-log-retention'),
                    
                    // Blacklist настройки
                    blacklistEnabledCheckbox: document.getElementById('setting-blacklist-enabled'),
                    autoBlacklistCheckbox: document.getElementById('setting-auto-blacklist'),
                    
                    // Модали
                    loadingOverlay: document.getElementById('loading')
                };
            },

            // Свързване на събития
            bindSettingsEvents: function() {
                const self = this;

                // Форма за настройки - submit
                if (self.fakeDetectorSettings.elements.settingsForm) {
                    self.fakeDetectorSettings.elements.settingsForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveSettings();
                    });
                }

                // Запазване на настройките (ако има отделен бутон)
                if (self.fakeDetectorSettings.elements.saveButton) {
                    self.fakeDetectorSettings.elements.saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveSettings();
                    });
                }

                // Възстановяване на настройките
                if (self.fakeDetectorSettings.elements.resetButton) {
                    self.fakeDetectorSettings.elements.resetButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.resetSettings();
                    });
                }

                // Тестване на настройките
                if (self.fakeDetectorSettings.elements.testButton) {
                    self.fakeDetectorSettings.elements.testButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testSettings();
                    });
                }

                // Експорт на настройките
                if (self.fakeDetectorSettings.elements.exportButton) {
                    self.fakeDetectorSettings.elements.exportButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.exportSettings();
                    });
                }

                // Импорт на настройките
                if (self.fakeDetectorSettings.elements.importFileInput) {
                    self.fakeDetectorSettings.elements.importFileInput.addEventListener('change', function(e) {
                        self.importSettings(e.target.files[0]);
                    });
                }

                // Промяна на enabled checkbox
                if (self.fakeDetectorSettings.elements.enabledCheckbox) {
                    self.fakeDetectorSettings.elements.enabledCheckbox.addEventListener('change', function() {
                        self.toggleDependentSettings();
                    });
                }

                // Промяна на auto scan checkbox
                if (self.fakeDetectorSettings.elements.autoScanCheckbox) {
                    self.fakeDetectorSettings.elements.autoScanCheckbox.addEventListener('change', function() {
                        self.toggleScanIntervalField();
                    });
                }

                // Промяна на email enabled checkbox
                if (self.fakeDetectorSettings.elements.emailEnabledCheckbox) {
                    self.fakeDetectorSettings.elements.emailEnabledCheckbox.addEventListener('change', function() {
                        self.toggleEmailFields();
                    });
                }

                // Промяна на log enabled checkbox
                if (self.fakeDetectorSettings.elements.logEnabledCheckbox) {
                    self.fakeDetectorSettings.elements.logEnabledCheckbox.addEventListener('change', function() {
                        self.toggleLogFields();
                    });
                }

                // Промяна на blacklist enabled checkbox
                if (self.fakeDetectorSettings.elements.blacklistEnabledCheckbox) {
                    self.fakeDetectorSettings.elements.blacklistEnabledCheckbox.addEventListener('change', function() {
                        self.toggleBlacklistFields();
                    });
                }

                // Валидация на полетата в реално време
                if (self.fakeDetectorSettings.elements.emailAddressInput) {
                    self.fakeDetectorSettings.elements.emailAddressInput.addEventListener('input', function() {
                        self.validateEmailAddress();
                    });
                }

                if (self.fakeDetectorSettings.elements.scanIntervalInput) {
                    self.fakeDetectorSettings.elements.scanIntervalInput.addEventListener('input', function() {
                        self.validateScanInterval();
                    });
                }

                if (self.fakeDetectorSettings.elements.maxCustomersInput) {
                    self.fakeDetectorSettings.elements.maxCustomersInput.addEventListener('input', function() {
                        self.validateMaxCustomers();
                    });
                }

                if (self.fakeDetectorSettings.elements.logRetentionInput) {
                    self.fakeDetectorSettings.elements.logRetentionInput.addEventListener('input', function() {
                        self.validateLogRetention();
                    });
                }
            },

            // Инициализация на компоненти
            initSettingsComponents: function() {
                this.storeOriginalValues();
                this.toggleDependentSettings();
                this.toggleScanIntervalField();
                this.toggleEmailFields();
                this.toggleLogFields();
                this.toggleBlacklistFields();
                this.validateAllFields();
            },

            // Запазване на оригиналните стойности
            storeOriginalValues: function() {
                const elements = this.fakeDetectorSettings.elements;
                const original = this.fakeDetectorSettings.originalValues;
                
                // Checkbox стойности
                original.enabled = elements.enabledCheckbox ? elements.enabledCheckbox.checked : false;
                original.autoScan = elements.autoScanCheckbox ? elements.autoScanCheckbox.checked : false;
                original.emailEnabled = elements.emailEnabledCheckbox ? elements.emailEnabledCheckbox.checked : false;
                original.logEnabled = elements.logEnabledCheckbox ? elements.logEnabledCheckbox.checked : false;
                original.blacklistEnabled = elements.blacklistEnabledCheckbox ? elements.blacklistEnabledCheckbox.checked : false;
                original.autoBlacklist = elements.autoBlacklistCheckbox ? elements.autoBlacklistCheckbox.checked : false;
                
                // Input стойности
                original.scanInterval = elements.scanIntervalInput ? elements.scanIntervalInput.value : '';
                original.maxCustomers = elements.maxCustomersInput ? elements.maxCustomersInput.value : '';
                original.emailAddress = elements.emailAddressInput ? elements.emailAddressInput.value : '';
                original.emailSubject = elements.emailSubjectInput ? elements.emailSubjectInput.value : '';
                original.logLevel = elements.logLevelSelect ? elements.logLevelSelect.value : '';
                original.logRetention = elements.logRetentionInput ? elements.logRetentionInput.value : '';
                
                // Правила
                original.rules = [];
                elements.rulesCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        original.rules.push(checkbox.value);
                    }
                });
            },

            // Запазване на настройките
            saveSettings: function() {
                const self = this;
                
                if (!self.validateAllFields()) {
                    self.showAlert('error', 'Моля поправете грешките преди запазване');
                    return;
                }
                
                const settings = self.getSettingsData();
                
                self.showSettingsLoading('Запазване на настройките...');
                
                fetch(self.fakeDetectorSettings.config.saveUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(settings)
                })
                .then(response => response.json())
                .then(data => {
                    self.hideSettingsLoading();
                    
                    if (data.success) {
                        self.showAlert('success', data.message || 'Настройките са запазени успешно');
                        
                        // Обновяване на оригиналните стойности
                        self.storeOriginalValues();
                    } else {
                        self.showAlert('error', data.error || 'Възникна грешка при запазването');
                    }
                })
                .catch(error => {
                    self.hideSettingsLoading();
                    console.error('Save settings error:', error);
                    self.showAlert('error', 'Възникна грешка при запазването');
                });
            },

            // Възстановяване на настройките
            resetSettings: function() {
                if (!confirm('Сигурни ли сте, че искате да възстановите оригиналните настройки?')) {
                    return;
                }
                
                const elements = this.fakeDetectorSettings.elements;
                const original = this.fakeDetectorSettings.originalValues;
                
                // Възстановяване на checkbox стойности
                if (elements.enabledCheckbox) elements.enabledCheckbox.checked = original.enabled;
                if (elements.autoScanCheckbox) elements.autoScanCheckbox.checked = original.autoScan;
                if (elements.emailEnabledCheckbox) elements.emailEnabledCheckbox.checked = original.emailEnabled;
                if (elements.logEnabledCheckbox) elements.logEnabledCheckbox.checked = original.logEnabled;
                if (elements.blacklistEnabledCheckbox) elements.blacklistEnabledCheckbox.checked = original.blacklistEnabled;
                if (elements.autoBlacklistCheckbox) elements.autoBlacklistCheckbox.checked = original.autoBlacklist;
                
                // Възстановяване на input стойности
                if (elements.scanIntervalInput) elements.scanIntervalInput.value = original.scanInterval;
                if (elements.maxCustomersInput) elements.maxCustomersInput.value = original.maxCustomers;
                if (elements.emailAddressInput) elements.emailAddressInput.value = original.emailAddress;
                if (elements.emailSubjectInput) elements.emailSubjectInput.value = original.emailSubject;
                if (elements.logLevelSelect) elements.logLevelSelect.value = original.logLevel;
                if (elements.logRetentionInput) elements.logRetentionInput.value = original.logRetention;
                
                // Възстановяване на правилата
                elements.rulesCheckboxes.forEach(checkbox => {
                    checkbox.checked = original.rules.includes(checkbox.value);
                });
                
                // Обновяване на зависимите полета
                this.toggleDependentSettings();
                this.toggleScanIntervalField();
                this.toggleEmailFields();
                this.toggleLogFields();
                this.toggleBlacklistFields();
                
                this.showAlert('info', 'Настройките са възстановени');
            },

            // Тестване на настройките
            testSettings: function() {
                const self = this;
                
                if (!self.validateAllFields()) {
                    self.showAlert('error', 'Моля поправете грешките преди тестване');
                    return;
                }
                
                const settings = self.getSettingsData();
                
                self.showSettingsLoading('Тестване на настройките...');
                
                fetch(self.fakeDetectorSettings.config.testUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(settings)
                })
                .then(response => response.json())
                .then(data => {
                    self.hideSettingsLoading();
                    
                    if (data.success) {
                        self.showAlert('success', data.message || 'Настройките са валидни');
                        
                        // Показване на тестовите резултати
                        if (data.test_results) {
                            self.showTestResults(data.test_results);
                        }
                    } else {
                        self.showAlert('error', data.error || 'Настройките имат проблеми');
                    }
                })
                .catch(error => {
                    self.hideSettingsLoading();
                    console.error('Test settings error:', error);
                    self.showAlert('error', 'Възникна грешка при тестването');
                });
            },

            // Получаване на данните от настройките
            getSettingsData: function() {
                const elements = this.fakeDetectorSettings.elements;

                // Събиране на всички данни от формата
                const formData = new FormData(elements.settingsForm);
                const settings = {
                    user_token: this.config.userToken
                };

                // Преобразуване на FormData в обект
                for (let [key, value] of formData.entries()) {
                    if (key.startsWith('settings[') && key.endsWith(']')) {
                        // Извличане на името на настройката
                        const settingName = key.slice(9, -1); // премахване на 'settings[' и ']'
                        settings[settingName] = value;
                    }
                }

                // Специална обработка за checkboxes (ако не са checked, не се включват в FormData)
                const checkboxes = elements.settingsForm.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    if (checkbox.name.startsWith('settings[') && checkbox.name.endsWith(']')) {
                        const settingName = checkbox.name.slice(9, -1);
                        settings[settingName] = checkbox.checked ? '1' : '0';
                    }
                });

                return settings;
            },

            // Toggle на зависими настройки
            toggleDependentSettings: function() {
                const elements = this.fakeDetectorSettings.elements;
                const isEnabled = elements.enabledCheckbox ? elements.enabledCheckbox.checked : false;

                // Деактивиране на всички полета ако модулът е изключен
                const dependentElements = [
                    elements.autoScanCheckbox,
                    elements.scanIntervalInput,
                    elements.maxCustomersInput,
                    elements.emailEnabledCheckbox,
                    elements.emailAddressInput,
                    elements.emailSubjectInput,
                    elements.logEnabledCheckbox,
                    elements.logLevelSelect,
                    elements.logRetentionInput,
                    elements.blacklistEnabledCheckbox,
                    elements.autoBlacklistCheckbox
                ];

                dependentElements.forEach(element => {
                    if (element) {
                        element.disabled = !isEnabled;
                    }
                });

                elements.rulesCheckboxes.forEach(checkbox => {
                    checkbox.disabled = !isEnabled;
                });
            },

            // Toggle на scan interval поле
            toggleScanIntervalField: function() {
                const elements = this.fakeDetectorSettings.elements;
                const isAutoScanEnabled = elements.autoScanCheckbox ? elements.autoScanCheckbox.checked : false;

                if (elements.scanIntervalInput) {
                    elements.scanIntervalInput.disabled = !isAutoScanEnabled;
                }
            },

            // Toggle на email полета
            toggleEmailFields: function() {
                const elements = this.fakeDetectorSettings.elements;
                const isEmailEnabled = elements.emailEnabledCheckbox ? elements.emailEnabledCheckbox.checked : false;

                if (elements.emailAddressInput) {
                    elements.emailAddressInput.disabled = !isEmailEnabled;
                }

                if (elements.emailSubjectInput) {
                    elements.emailSubjectInput.disabled = !isEmailEnabled;
                }
            },

            // Toggle на log полета
            toggleLogFields: function() {
                const elements = this.fakeDetectorSettings.elements;
                const isLogEnabled = elements.logEnabledCheckbox ? elements.logEnabledCheckbox.checked : false;

                if (elements.logLevelSelect) {
                    elements.logLevelSelect.disabled = !isLogEnabled;
                }

                if (elements.logRetentionInput) {
                    elements.logRetentionInput.disabled = !isLogEnabled;
                }
            },

            // Toggle на blacklist полета
            toggleBlacklistFields: function() {
                const elements = this.fakeDetectorSettings.elements;
                const isBlacklistEnabled = elements.blacklistEnabledCheckbox ? elements.blacklistEnabledCheckbox.checked : false;

                if (elements.autoBlacklistCheckbox) {
                    elements.autoBlacklistCheckbox.disabled = !isBlacklistEnabled;
                }
            },

            // Валидация на email адрес
            validateEmailAddress: function() {
                const elements = this.fakeDetectorSettings.elements;
                if (!elements.emailAddressInput) return true;

                const email = elements.emailAddressInput.value.trim();

                if (!email) {
                    elements.emailAddressInput.classList.remove('border-red-300', 'border-green-300');
                    return true;
                }

                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                if (!emailRegex.test(email)) {
                    elements.emailAddressInput.classList.remove('border-green-300');
                    elements.emailAddressInput.classList.add('border-red-300');
                    return false;
                } else {
                    elements.emailAddressInput.classList.remove('border-red-300');
                    elements.emailAddressInput.classList.add('border-green-300');
                    return true;
                }
            },

            // Валидация на scan interval
            validateScanInterval: function() {
                const elements = this.fakeDetectorSettings.elements;
                if (!elements.scanIntervalInput) return true;

                const value = parseInt(elements.scanIntervalInput.value);

                if (isNaN(value) || value < 1 || value > 1440) {
                    elements.scanIntervalInput.classList.remove('border-green-300');
                    elements.scanIntervalInput.classList.add('border-red-300');
                    return false;
                } else {
                    elements.scanIntervalInput.classList.remove('border-red-300');
                    elements.scanIntervalInput.classList.add('border-green-300');
                    return true;
                }
            },

            // Валидация на max customers
            validateMaxCustomers: function() {
                const elements = this.fakeDetectorSettings.elements;
                if (!elements.maxCustomersInput) return true;

                const value = parseInt(elements.maxCustomersInput.value);

                if (isNaN(value) || value < 1 || value > 10000) {
                    elements.maxCustomersInput.classList.remove('border-green-300');
                    elements.maxCustomersInput.classList.add('border-red-300');
                    return false;
                } else {
                    elements.maxCustomersInput.classList.remove('border-red-300');
                    elements.maxCustomersInput.classList.add('border-green-300');
                    return true;
                }
            },

            // Валидация на log retention
            validateLogRetention: function() {
                const elements = this.fakeDetectorSettings.elements;
                if (!elements.logRetentionInput) return true;

                const value = parseInt(elements.logRetentionInput.value);

                if (isNaN(value) || value < 1 || value > 365) {
                    elements.logRetentionInput.classList.remove('border-green-300');
                    elements.logRetentionInput.classList.add('border-red-300');
                    return false;
                } else {
                    elements.logRetentionInput.classList.remove('border-red-300');
                    elements.logRetentionInput.classList.add('border-green-300');
                    return true;
                }
            },

            // Валидация на всички полета
            validateAllFields: function() {
                let isValid = true;

                if (!this.validateEmailAddress()) isValid = false;
                if (!this.validateScanInterval()) isValid = false;
                if (!this.validateMaxCustomers()) isValid = false;
                if (!this.validateLogRetention()) isValid = false;

                // Проверка дали има избрани правила
                const elements = this.fakeDetectorSettings.elements;
                const selectedRules = Array.from(elements.rulesCheckboxes).filter(cb => cb.checked);

                if (elements.enabledCheckbox && elements.enabledCheckbox.checked && selectedRules.length === 0) {
                    this.showAlert('warning', 'Моля изберете поне едно правило за детекция');
                    isValid = false;
                }

                return isValid;
            },

            // Показване на loading overlay
            showSettingsLoading: function(message = 'Зареждане...') {
                const overlay = this.fakeDetectorSettings.elements.loadingOverlay;
                if (overlay) {
                    const messageElement = overlay.querySelector('span');
                    if (messageElement) {
                        messageElement.textContent = message;
                    }
                    overlay.classList.remove('hidden');
                }
            },

            // Скриване на loading overlay
            hideSettingsLoading: function() {
                const overlay = this.fakeDetectorSettings.elements.loadingOverlay;
                if (overlay) {
                    overlay.classList.add('hidden');
                }
            },

            // Показване на тестови резултати
            showTestResults: function(results) {
                let message = 'Тестови резултати:\n\n';

                if (results.email_test) {
                    message += `Email тест: ${results.email_test.success ? 'Успешен' : 'Неуспешен'}\n`;
                    if (results.email_test.message) {
                        message += `Съобщение: ${results.email_test.message}\n`;
                    }
                }

                if (results.database_test) {
                    message += `\nБаза данни тест: ${results.database_test.success ? 'Успешен' : 'Неуспешен'}\n`;
                    if (results.database_test.message) {
                        message += `Съобщение: ${results.database_test.message}\n`;
                    }
                }

                if (results.rules_test) {
                    message += `\nПравила тест: ${results.rules_test.success ? 'Успешен' : 'Неуспешен'}\n`;
                    if (results.rules_test.message) {
                        message += `Съобщение: ${results.rules_test.message}\n`;
                    }
                }

                alert(message);
            },

            /**
             * Debug логване
             */
            log: function(message, data) {
                if (window.console && window.console.log) {
                    console.log('[FakeDetectorModule] ' + message, data || '');
                }
            },

            // Експорт на настройките
            exportSettings: function() {
                const self = this;

                self.showSettingsLoading('Експорт на настройките...');

                fetch(self.fakeDetectorSettings.config.exportUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.blob();
                })
                .then(blob => {
                    self.hideSettingsLoading();

                    // Създаване на линк за сваляне
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'fake_detector_settings_' + new Date().toISOString().slice(0, 10) + '.json';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    self.showAlert('success', 'Настройките са експортирани успешно');
                })
                .catch(error => {
                    self.hideSettingsLoading();
                    console.error('Export settings error:', error);
                    self.showAlert('error', 'Възникна грешка при експорта');
                });
            },

            // Импорт на настройките
            importSettings: function(file) {
                const self = this;

                if (!file) {
                    self.showAlert('error', 'Моля изберете файл за импорт');
                    return;
                }

                if (file.type !== 'application/json') {
                    self.showAlert('error', 'Моля изберете JSON файл');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const settings = JSON.parse(e.target.result);
                        self.applyImportedSettings(settings);
                    } catch (error) {
                        self.showAlert('error', 'Невалиден JSON файл');
                    }
                };
                reader.readAsText(file);
            },

            // Прилагане на импортираните настройки
            applyImportedSettings: function(settings) {
                const self = this;

                if (!settings || typeof settings !== 'object') {
                    self.showAlert('error', 'Невалидни данни за настройки');
                    return;
                }

                // Попълване на полетата с импортираните стойности
                const form = self.fakeDetectorSettings.elements.settingsForm;
                if (!form) {
                    self.showAlert('error', 'Формата за настройки не е намерена');
                    return;
                }

                let appliedCount = 0;

                Object.keys(settings).forEach(key => {
                    if (key === 'user_token') return; // Пропускаме user_token

                    const input = form.querySelector(`[name="settings[${key}]"]`);
                    if (input) {
                        if (input.type === 'checkbox') {
                            input.checked = settings[key] === '1' || settings[key] === true;
                        } else {
                            input.value = settings[key];
                        }
                        appliedCount++;
                    }
                });

                if (appliedCount > 0) {
                    self.showAlert('success', `Импортирани са ${appliedCount} настройки. Моля запазете промените.`);
                } else {
                    self.showAlert('warning', 'Не са намерени съвместими настройки за импорт');
                }

                // Изчистване на file input
                if (self.fakeDetectorSettings.elements.importFileInput) {
                    self.fakeDetectorSettings.elements.importFileInput.value = '';
                }
            }

        });
    }

})();
